// eslint-disable-next-line
/* eslint-disable unicorn/prefer-number-properties */
/* eslint-disable no-nested-ternary */
import {
  useCallback,
  useContext,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { isEmpty } from 'lodash'
import { Tooltip } from '@karoo-ui/core'
import { useQueryClient } from '@tanstack/react-query'
//utils
import moment from 'moment'
import type { OnDragEndResponder } from 'react-beautiful-dnd'
import { FormattedMessage } from 'react-intl'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory, useLocation } from 'react-router'
import * as R from 'remeda'

//duxs
import { getSettings } from 'duxs/user'
import Icon from 'src/components/Icon'
import { getDriverDetailsModalMainPath } from 'src/modules/app/GlobalModals/DriverDetails'
//context
import { DeliveryContext } from 'src/modules/delivery'
import useDeliveryCalculateStopLegsMutation from 'src/modules/delivery/api/drivers/useDeliveryCalculateStopLegs'
import useDriverOptimizeRouteMutation from 'src/modules/delivery/api/drivers/useDeliveryDriverOptimizeRoute'
//api
import useDeliveryDriversLocationQuery, {
  type FetchDeliveryDriversLocation,
} from 'src/modules/delivery/api/drivers/useDeliveryDriversLocationQuery'
import type { FetchGetDeliveryDrivers } from 'src/modules/delivery/api/drivers/useDeliveryGetDrivers'
// DWA-3131 Disable Chat
// import ChatPanelTitle from './Chat/panel-title'

import { useMutationUpdateStops } from 'src/modules/delivery/api/drivers/useDeliveryReorderStopsMutation'
import { StopStatus } from 'src/modules/delivery/api/jobs/types'
//types
import type {
  CountriesReturn,
  DriverStatusReturn,
} from 'src/modules/delivery/api/lookup/types'
import useGetLookQuery from 'src/modules/delivery/api/lookup/useGetLookupQuery'
import Box from 'src/modules/delivery/components/Box'
import Button from 'src/modules/delivery/components/Buttons'
import JobItem from 'src/modules/delivery/components/List/Jobs/Item/ItemView'
import LoadingOverlay from 'src/modules/delivery/components/Loading/PanelOverlay'
import LockedStatusCard from 'src/modules/delivery/components/LockedStatusCard'
import Panel from 'src/modules/delivery/components/Panel'
import Progress from 'src/modules/delivery/components/ProgressBar'
import Switch from 'src/modules/delivery/components/Switch'
import Tabs from 'src/modules/delivery/components/Tab'
import { themeType } from 'src/modules/delivery/components/Theme'
// import { setIsChatOpen } from 'src/modules/delivery/duxs/chat'
//hooks
import useDeliveryDriverCapacityData from 'src/modules/delivery/hooks/drivers/useDeliveryDriverCapacityData'
import useGetActiveRoutingSettings from 'src/modules/delivery/hooks/settings/useGetActiveRoutingSettings'
import useInterval from 'src/modules/delivery/hooks/useInterval'
import useDeleteDriverHook from 'src/modules/delivery/right-panel/Driver/hooks/useDeleteDriver'
import BasicInfo from 'src/modules/delivery/right-panel/Driver/view/basicinfo'
import LoginInfo from 'src/modules/delivery/right-panel/Driver/view/logininfo'
import RouteOptimizeButton from 'src/modules/delivery/right-panel/Driver/view/optimize-route'
import OwnershipInfo from 'src/modules/delivery/right-panel/Driver/view/ownershipinfo'
import {
  changedJobStopsSequence,
  getIsChangingJobStopsSequence,
  startedChangingJobStopsSequence,
  stoppedChangingJobStopsSequence,
  unmountedDriverRightPanel,
} from 'src/modules/delivery/right-panel/Driver/view/slice'
import {
  StyledText,
  SwitchWrapper,
} from 'src/modules/delivery/right-panel/Driver/view/styled'
import VehicleInfo from 'src/modules/delivery/right-panel/Driver/view/vehicleinfo'
import { ROUTING_PROVIDER_API } from 'src/modules/delivery/setting/routing'
import {
  clickedOverviewJobDetail,
  getDriverPostDataState,
  getFocusedDeliveryDriverIds,
  resetDriverPostDataState,
} from 'src/modules/delivery/slice'
//components
import {
  SplitContainer,
  SplitContainerWrapper,
} from 'src/modules/delivery/styled/GlobalStyles'
import checkSubUserAccessible, {
  canThisUserSeeSubuser,
} from 'src/modules/delivery/utils/checkSubUserAccessible'
//constants
import {
  DEFAULT_FIELD_SERVICE_TEXT,
  DEFAULT_WORKER_TEXT,
  DELIVERY_COLOR,
  DRIVER_STATUS_TO_ID,
  ETA_SIMULATOR_STATUS_ID,
  JOB_STATUS_TO_ID,
  JOB_STOP_TYPE_ID,
  SCHEDULE_TYPE_ID,
} from 'src/modules/delivery/utils/constants'
import Dialog from 'src/modules/delivery/utils/dialog/dialog-configurator'
import type { FixMeAny } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

import DriverIsCapableIcon from 'assets/svg/Delivery/job-status-completed.svg'
import DriverIsInCapableIcon from 'assets/svg/Delivery/job-status-rejected.svg'
//svg
import DriverNoJobTodo from 'assets/svg/Delivery/panel-no-job.svg'

import { handleShowRoutingError } from '../../AssignJob/AssignJob'
import ETASimulator from '../../component/eta-simulate-button'
import useUpdateDriverStatus from '../hooks/useUpdateDriverStatus'
import DriverStatusDetails from '../view/status-details'
import useDeliveryDriverStopsData from './assignjobs/useDeliveryDriverStopsData'
import { tabList } from './constants'
// DWA-3131 Disable Chat
// import Chat from './Chat'
import CurrentStopStartLocation from './job-route-cards/current-stops'
import FutureStops from './job-route-cards/future-stops'
import DriverLoginModal from './modals/driver-login'
//styled
import {
  ButtonSequenceWrapper,
  ChangingSequenceWrapper,
  JobItemContainer,
  // DWA-3131 Disable Chat
  // SplitBottomPart,
  // MessageTip,
  RouteAndETASimulatorWrapper,
  RouteDriverStatusSVG,
  RouterDriverText,
  RouteStatusWrapper,
  SplitTopPart,
} from './styled'

// DWA-3131 Disable Chat
// import { useChatByDriverId } from 'src/modules/delivery/api/chat'

type Props = {
  driver: FetchGetDeliveryDrivers.Driver | undefined
}

const DriverInfo = ({ driver }: Props) => {
  const history = useHistory()
  const routerLocation = useLocation()
  const queryClient = useQueryClient()
  const focusedDeliveryDriverIds = useSelector(getFocusedDeliveryDriverIds)

  const isChangingJobStopsSequence = useSelector(getIsChangingJobStopsSequence)

  const [defaultETAISOString, setDefaultETAISOString] = useState(
    moment().add(60, 'seconds').toISOString(),
  )

  useInterval(() => {
    if (isChangingJobStopsSequence) return
    setDefaultETAISOString(moment().add(60, 'seconds').toISOString())
  }, 60000)

  const [etaSimulateValue, setETASimulateValue] = useState<Date | null>(null)

  const deliveryDriverJobs = useDeliveryDriverStopsData(focusedDeliveryDriverIds[0], {
    defaultETAISOString,
    etaSimulateValue,
  })
  const { deliverySettings } = useContext(DeliveryContext)
  const [currentTab, setCurrentTab] = useState<string>('1')

  const [draggingIndex, setDraggingIndex] = useState<number | null>(null)

  const [modal, setModal] = useState<JSX.Element | null>(null)
  const dispatch = useDispatch()
  const { mutate, isPending } = useMutationUpdateStops({
    onSettled: () => {
      dispatch(stoppedChangingJobStopsSequence())
    },
  })

  const totalJobsOpimizationWarning = useMemo(() => {
    const jobs = deliveryDriverJobs.deduplicatedJobs

    let totalJobPicupMaxLimit = 0
    for (const job of Object.values(jobs)) {
      if (
        job.jobStatusId !== JOB_STATUS_TO_ID.COMPLETED &&
        job.scheduleTypeId !== SCHEDULE_TYPE_ID.SCHEDULE
      ) {
        totalJobPicupMaxLimit++
      }
    }

    return totalJobPicupMaxLimit
  }, [deliveryDriverJobs.deduplicatedJobs])

  const defaultStops = useMemo(
    () => ({
      stops: deliveryDriverJobs.stops,
      currentStops: deliveryDriverJobs.currentStops,
      futureStops: deliveryDriverJobs.futureStops,
      hasStartedJobs: deliveryDriverJobs.hasStartedJobs,
      legs: deliveryDriverJobs.legs,
    }),
    [deliveryDriverJobs],
  )
  // TODO: Revert the logic to set the status details to open
  const [jobStopInfo, setJobStopInfo] = useState(defaultStops)
  const [isFoldedDriverDetails, setIsFoldedDriverDetails] = useState(true)
  const [isFoldedDriverStopDetails, setIsFoldedDriverStopDetails] = useState(false)
  const [isFoldedStatusDetails, setIsFoldedStatusDetails] = useState(false)
  const [isOpenedBasicInfo, setIsOpenedBasicInfo] = useState(true)
  const [isOpenedLoginInfo, setIsOpenedLoginInfo] = useState(true)
  const [isOpenedVehicleInfo, setIsOpenedVehicleInfo] = useState(true)
  const [isOpenedOwndership, setIsOpenedOwndership] = useState(true)
  // DWA-3131 Disable Chat
  const [isFoldedChat] = useState(false)

  // useEffect(() => {
  //   dispatch(setIsChatOpen(!isFoldedChat))
  // }, [isFoldedChat, dispatch])

  const driverParsedJobs = JSON.stringify(deliveryDriverJobs?.parsedJobs)
  const recalculateStopLegs = useDeliveryCalculateStopLegsMutation()
  const postDriverState = useSelector(getDriverPostDataState)

  useEffect(() => {
    setJobStopInfo(defaultStops)
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [driverParsedJobs, etaSimulateValue, defaultETAISOString])

  useEffect(
    () => () => {
      dispatch(unmountedDriverRightPanel())
    },
    [dispatch],
  )

  useEffect(() => {
    dispatch(stoppedChangingJobStopsSequence())
    setETASimulateValue(null)
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [focusedDeliveryDriverIds])

  /**
   * This index is used to determine range of non swappable item
   * e.g current drag stop is a drop-off, the swapBreakingPoint would be the corresponding pickup
   * Stops between 0 to the swapBreakingPoint are not swappable by current dragging stop.
   * This is to ensure the business logic of no drop-off stop should appear before corresponding pickup stop
   * null indicating no dragging action
   */
  const { swapBreakingPoint, isDraggingPickupStop } = useMemo(() => {
    const defaultState = { swapBreakingPoint: null, isDraggingPickupStop: false }
    if (draggingIndex === null || jobStopInfo.currentStops.length === 0)
      return defaultState
    const draggedStop = jobStopInfo.currentStops[draggingIndex]
    if (!draggedStop) return defaultState
    const isDraggingPickupStop = draggedStop?.stopTypeId === JOB_STOP_TYPE_ID.PICKUP
    const correspondingStop = jobStopInfo.currentStops.find(
      (stop) =>
        stop.jobId === draggedStop.jobId && stop.ordering !== draggedStop.ordering,
    )

    return {
      swapBreakingPoint: correspondingStop
        ? correspondingStop.ordering - 1 // Ordering start from 1, so we need to minus the offset
        : null,
      isDraggingPickupStop,
    }
  }, [draggingIndex, jobStopInfo])

  const moveCard = useCallback<OnDragEndResponder>(
    ({ source, destination }) => {
      if (!destination) return
      const dragIndex = source.index
      const hoverIndex = destination.index
      const draggedItem = jobStopInfo.currentStops[dragIndex]
      setJobStopInfo((prevState) => {
        const newItems = prevState.currentStops.filter(
          (s) => draggedItem.stopId !== s.stopId,
        )
        newItems.splice(hoverIndex, 0, draggedItem)

        const updatedItem = newItems.map((item, index) => ({
          ...item,
          ordering: index + 1,
        }))

        //Set new dragging index after swap happened for ensuring an accurate swapBreakingPoint
        setDraggingIndex((_) => hoverIndex)

        dispatch(changedJobStopsSequence([...updatedItem]))
        return { ...prevState, currentStops: updatedItem }
      })
      setDraggingIndex(null)
    },
    [dispatch, jobStopInfo],
  )

  const lookup = useGetLookQuery()
  let driverStatusLookup: DriverStatusReturn = []
  let countryOptions: CountriesReturn = []

  if (lookup.status === 'success') {
    driverStatusLookup = lookup.data.driverStatus
    countryOptions = lookup.data.countryOptions
  }

  const startStop = driver?.startLocationCustomer
  const endStop = driver?.endLocationCustomer
  const deliveryDriverMetricCapacity = useDeliveryDriverCapacityData({
    deliveryDriverId: focusedDeliveryDriverIds[0],
    maxWeight: Number(driver?.maxWeight) || 0,
    maxVolume: Number(driver?.maxVolume) || 0,
    scheduleShift: {
      start: driver?.shiftTimeStart || '',
      end: driver?.shiftTimeEnd || '',
    },
    deliveryCapabilities: driver?.deliveryCapabilities || [],
    jobs: deliveryDriverJobs.parsedJobs,
  })

  const data = queryClient.getQueryData<FetchDeliveryDriversLocation.Return>(
    useDeliveryDriversLocationQuery.createKey(),
  )

  const focusedDriverLocation =
    data && data?.find((location) => location.deliveryDriverId === driver?.driverId)

  const statusDetailsData = {
    volume: deliveryDriverMetricCapacity?.volume,
    weight: deliveryDriverMetricCapacity?.weight,
    driverCapabilities: deliveryDriverMetricCapacity?.driverCapabilities || [],
    shiftSchedule: {
      start: driver?.shiftTimeStart || '',
      end: driver?.shiftTimeEnd || '',
    },
    isScheduleCapable: deliveryDriverMetricCapacity?.isScheduleCapable,
  }

  const routeInfoDetailsData =
    deliveryDriverJobs?.totalProjectedDistance ||
    deliveryDriverJobs?.totalProjectedTravelTime ||
    focusedDriverLocation?.coords
      ? {
          distance: deliveryDriverJobs?.totalProjectedDistance,
          duration: deliveryDriverJobs?.totalProjectedTravelTime,
          coordinates: focusedDriverLocation?.coords,
        }
      : null

  const updateDriverStatus = useUpdateDriverStatus(
    driver?.driverId || '',
    !driver?.isActive,
    driver?.fleetDriverId,
  )

  const deleteDriverMutation = useDeleteDriverHook(driver?.driverId)

  const moreOptionItems = useMemo(
    () => [
      {
        value: 'edit',
        label: ctIntl.formatMessage({
          id: 'delivery.rightPanel.driverInfo.option.edit',
        }),
        onClick: () => {
          if (driver?.fleetDriverId) {
            history.push(
              getDriverDetailsModalMainPath(
                routerLocation,
                driver.fleetDriverId,
                'DELIVERY',
              ),
            )
          }
        },
        disabled: !driver?.fleetDriverId,
      },
      //We will comment this for now as the feature will be available on the next release
      // {
      //   value: 'deactivate',
      //   label: ctIntl.formatMessage({
      //     id: 'delivery.rightPanel.driverInfo.option.deactivate',
      //   }),
      // },
      // {
      //   value: 'duplicate',
      //   label: ctIntl.formatMessage({
      //     id: 'delivery.rightPanel.driverInfo.option.duplicate',
      //   }),
      // },
      {
        value: 'delete',
        label: ctIntl.formatMessage({
          id: 'delivery.rightPanel.driverInfo.option.delete',
        }),
        onClick: () => {
          if (defaultStops.stops.length > 0) {
            Dialog.alert({
              title: ctIntl.formatMessage({
                id: 'Deletion Failed',
              }),
              content: ctIntl.formatMessage({
                id: 'Drivers cannot be deleted while having jobs. Please make sure no jobs on the driver’s route.',
              }),
              hideConfirmButton: true,
              rejectButtonLabel: ctIntl.formatMessage({
                id: 'Got it',
              }),
            })
          } else {
            Dialog.alert({
              title: (
                <>
                  <div>
                    {ctIntl.formatMessage({
                      id: 'Are you sure you want to delete this Driver?',
                    })}
                  </div>
                  <Box
                    marginTop="5px"
                    fontWeight="bold"
                  >
                    {driver?.fullName}
                  </Box>
                </>
              ),
              content: ctIntl.formatMessage({
                id: 'You will not be able to restore it if proceed',
              }),
              onResult: () => {
                if (driver?.driverId) {
                  deleteDriverMutation.mutate({
                    id: driver.driverId,
                  })
                }
              },
              confirmButtonLabel: ctIntl.formatMessage({
                id: 'Delete',
              }),
            })
          }
        },
      },
    ],
    [
      defaultStops.stops.length,
      deleteDriverMutation,
      driver?.driverId,
      driver?.fleetDriverId,
      driver?.fullName,
      history,
      routerLocation,
    ],
  )

  const jobProgress = deliveryDriverJobs?.progressData?.jobProgressPercentage || 0

  const startedChangingJobStopsSequenceHandler = (index: number) => {
    setDraggingIndex(index)
    dispatch(startedChangingJobStopsSequence())
  }

  const assignedJobsRef = useRef<HTMLDivElement>(null)
  const svgIconRef = useRef<HTMLDivElement>(null)

  useLayoutEffect(() => {
    const currRef = svgIconRef.current
    const currAssignedJobRef = assignedJobsRef.current
    if (currRef && currAssignedJobRef) {
      const rect = currRef.getBoundingClientRect()
      const rectAssignedJobRef = currAssignedJobRef.getBoundingClientRect()
      const centeredIconCalc =
        rect.top > window.innerHeight * 0.8
          ? rect.top
          : rect.top + (rect.top - rectAssignedJobRef.top)
      if (rect) {
        currRef.style.height = `calc(100vh - ${centeredIconCalc}px)`
      }
    }
  }, [
    assignedJobsRef,
    jobStopInfo,
    deliveryDriverJobs.query.isPending,
    isFoldedDriverDetails,
    isFoldedStatusDetails,
    isOpenedBasicInfo,
    isOpenedLoginInfo,
    currentTab,
  ])

  const etaStatus = useMemo(() => {
    if (driver?.driverStatusId === DRIVER_STATUS_TO_ID.OFFLINE && !etaSimulateValue) {
      return ETA_SIMULATOR_STATUS_ID.DRIVER_UNLOCATED
    }
    return deliveryDriverJobs.driverETASimulationStatus
  }, [
    driver?.driverStatusId,
    deliveryDriverJobs.driverETASimulationStatus,
    etaSimulateValue,
  ])

  const { defaultissuingcountry } = useSelector(getSettings)
  const user = (JSON.parse as FixMeAny)(localStorage.getItem('user'))
  const countryData = countryOptions.find(
    (option) => option.countryCode === defaultissuingcountry,
  )

  const driverData = useMemo(
    () => ({
      account: user?.account || '',
      username: driver?.loginUsername || '',
      password: postDriverState?.driverPassword,
      country: countryData?.name || '',
    }),
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      driver?.loginUsername,
      defaultissuingcountry,
      user?.account,
      countryData?.name,
      postDriverState?.driverPassword,
    ],
  )

  const handleDriverLoginModal = () => {
    setModal(
      <DriverLoginModal
        setModalState={(value) => setModal(value)}
        driversInfo={driverData}
      />,
    )
  }
  useEffect(() => {
    if (
      postDriverState.isPostData &&
      focusedDeliveryDriverIds[0] === postDriverState.driverId &&
      driver?.loginUsername
    ) {
      handleDriverLoginModal()
    }

    if (
      postDriverState.isPostData &&
      focusedDeliveryDriverIds[0] !== postDriverState.driverId
    ) {
      dispatch(resetDriverPostDataState())
    }

    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    postDriverState.isPostData,
    postDriverState.driverId,
    focusedDeliveryDriverIds,
    driver?.loginUsername,
  ])

  const totalJobs = useMemo(
    () =>
      (deliveryDriverJobs.deduplicatedJobs &&
        R.isArray(deliveryDriverJobs.deduplicatedJobs) &&
        // eslint-disable-next-line
        // eslint-disable-next-line unicorn/explicit-length-check
        deliveryDriverJobs.deduplicatedJobs.length) ||
      0,
    [deliveryDriverJobs.deduplicatedJobs],
  )

  const isShowOptimizationWarning = useMemo(() => {
    if (deliverySettings.routingProviderApi === ROUTING_PROVIDER_API.PICUP) {
      return (
        totalJobsOpimizationWarning > Number.parseInt(deliverySettings.picupMaxJobs)
      )
    } else if (deliverySettings.routingProviderApi === ROUTING_PROVIDER_API.ROUTIFIC) {
      return (
        totalJobsOpimizationWarning > Number.parseInt(deliverySettings.routificMaxJobs)
      )
    }

    return false
  }, [
    totalJobsOpimizationWarning,
    deliverySettings.picupMaxJobs,
    deliverySettings.routificMaxJobs,
    deliverySettings.routingProviderApi,
  ])

  const optimizationMaxLimit = useMemo(() => {
    if (deliverySettings.routingProviderApi === ROUTING_PROVIDER_API.PICUP) {
      return deliverySettings.picupMaxJobs
    } else if (deliverySettings.routingProviderApi === ROUTING_PROVIDER_API.ROUTIFIC) {
      return deliverySettings.routificMaxJobs
    }

    return 0
  }, [
    deliverySettings.picupMaxJobs,
    deliverySettings.routificMaxJobs,
    deliverySettings.routingProviderApi,
  ])

  const disableOptimizeDriverStops = useMemo(() => {
    const undoneStatus = defaultStops.currentStops.filter(
      (stop) =>
        ![StopStatus.COMPLETED, StopStatus.REJECTED, StopStatus.ARRIVED].includes(
          stop.stopStatusId,
        ),
    )

    const stopsPD = undoneStatus.filter((stop) =>
      [JOB_STOP_TYPE_ID.PICKUP, JOB_STOP_TYPE_ID.DROPOFF].includes(stop.stopTypeId),
    )

    const stopsSingle = undoneStatus.filter((stop) =>
      [JOB_STOP_TYPE_ID.SINGLE].includes(stop.stopTypeId),
    )

    return (
      isEmpty(undoneStatus) ||
      (undoneStatus.length <= 2 && !isEmpty(stopsPD) && stopsPD.length <= 2) ||
      (undoneStatus.length === 1 && stopsSingle.length === 1)
    )
  }, [defaultStops.currentStops])

  const optimizeDriverStopsRoute = useDriverOptimizeRouteMutation()

  const { routingMinVisitsPerVehicle, routingDynamicBalance } =
    useGetActiveRoutingSettings()

  const handleOptimizeButton = () => {
    const calcValidStopsToOptimize = jobStopInfo.currentStops.length
    if (jobStopInfo.hasStartedJobs) {
      Dialog.alert({
        title: (
          <Box fontWeight="600">
            {ctIntl.formatMessage({
              id: 'Optimize started jobs?',
            })}
          </Box>
        ),
        content: (
          <Box
            marginTop="10px"
            textAlign="left"
          >
            {ctIntl.formatMessage({
              id: 'delivery.optimizeStartedJobs.subtext',
            })}
          </Box>
        ),
        confirmButtonLabel: ctIntl.formatMessage({
          id: 'Proceed',
        }),
        rejectButtonLabel: ctIntl.formatMessage({
          id: 'Quit',
        }),
        confirmButtonType: 'primary',
        onClose() {
          setModal(<></>)
        },
        onResult() {
          if (
            [ROUTING_PROVIDER_API.PICUP, ROUTING_PROVIDER_API.ROUTIFIC].includes(
              deliverySettings.routingProviderApi,
            ) &&
            routingDynamicBalance === 'false' &&
            !isNaN(+routingMinVisitsPerVehicle) &&
            +routingMinVisitsPerVehicle > calcValidStopsToOptimize
          ) {
            handleShowRoutingError(
              calcValidStopsToOptimize,
              +routingMinVisitsPerVehicle,
            )
          } else {
            optimizeDriverStopsRoute.mutate({
              driverId: focusedDeliveryDriverIds[0] || '',
            })
          }

          setModal(<></>)
        },
      })
    } else if (
      [ROUTING_PROVIDER_API.PICUP, ROUTING_PROVIDER_API.ROUTIFIC].includes(
        deliverySettings.routingProviderApi,
      ) &&
      routingDynamicBalance === 'false' &&
      !isNaN(+routingMinVisitsPerVehicle) &&
      +routingMinVisitsPerVehicle > calcValidStopsToOptimize
    ) {
      handleShowRoutingError(calcValidStopsToOptimize, +routingMinVisitsPerVehicle)
    } else {
      optimizeDriverStopsRoute.mutate({
        driverId: focusedDeliveryDriverIds[0] || '',
      })
    }
  }
  // DWA-3131 Disable Chat
  // const { unreadMessagesCount, markAllRead, isUserTyping, connection, isConnecting } =
  //   useChatByDriverId(driver?.driverId)
  // useEffect(() => {
  //   if (!isFoldedChat && unreadMessagesCount) {
  //     markAllRead()
  //   }
  // }, [isFoldedChat, markAllRead, unreadMessagesCount])

  return (
    <>
      {modal}
      <SplitContainerWrapper
        themeType={themeType['light']}
        cursor="row-resize"
      >
        <SplitContainer
          // DWA-3131 Disable Chat
          // sizes={isFoldedChat ? [100, 0] : [65, 35]}
          sizes={[100, 0]}
          direction="vertical"
          cursor="row-resize"
          // DWA-3131 Disable Chat
          // minSize={[90, 30]} // [The panel counts of top part * 30, The panel counts of bottom part * 30]
          gutterSize={3}
          disabled={isFoldedChat}
        >
          <SplitTopPart>
            <Panel
              isDisabledScroll={true}
              isFlexibleHeight={false}
              isFoldable
              paddingBottom="10px"
              panelHeaderProps={{
                isStickyTop: true,
                title: deliverySettings.driverLabel
                  ? `${deliverySettings.driverLabel} ${' '}${ctIntl.formatMessage({
                      id: 'Info',
                    })}`
                  : ctIntl.formatMessage({
                      id: `${
                        deliverySettings.fieldService
                          ? DEFAULT_FIELD_SERVICE_TEXT
                          : DEFAULT_WORKER_TEXT
                      } Info`,
                    }),
                customActions: (
                  <>
                    <StyledText>
                      {ctIntl.formatMessage({
                        id: `delivery.rightPanel.driverInfo.panel.${
                          driver?.isActive ? 'active' : 'inactive'
                        }`,
                      })}
                    </StyledText>
                    <SwitchWrapper>
                      <Switch
                        onChange={(value) => {
                          updateDriverStatus.mutate({
                            id: driver?.driverId || '',
                            is_active: value,
                          })
                        }}
                        defaultChecked={{ value: driver?.isActive }}
                        disabled={updateDriverStatus.isPending}
                      />
                    </SwitchWrapper>
                  </>
                ),
                moreActions: moreOptionItems,
              }}
              onFoldClick={() => setIsFoldedDriverDetails((prev) => !prev)}
              isFolded={isFoldedDriverDetails}
            >
              {driver && (
                <>
                  <BasicInfo
                    driver={driver}
                    isFolded={isOpenedBasicInfo}
                    onFoldedClick={() => setIsOpenedBasicInfo((prev) => !prev)}
                  />
                  <LoginInfo
                    driver={driver}
                    isFolded={isOpenedLoginInfo}
                    onFoldedClick={() => setIsOpenedLoginInfo((prev) => !prev)}
                    onShowDriverLogin={handleDriverLoginModal}
                  />
                  <VehicleInfo
                    driver={driver}
                    isFolded={isOpenedVehicleInfo}
                    onFoldedClick={() => setIsOpenedVehicleInfo((prev) => !prev)}
                  />
                  {/* Limiting the access of sub-user fields and will be controlled in the DB
                      [owned_plus_admin]: gives access to sub-user field and feature */}
                  {checkSubUserAccessible() && canThisUserSeeSubuser() && (
                    <OwnershipInfo
                      driver={driver}
                      isFolded={isOpenedOwndership}
                      onFoldedClick={() => setIsOpenedOwndership((prev) => !prev)}
                    />
                  )}
                </>
              )}
            </Panel>

            {deliveryDriverMetricCapacity && (
              <Panel
                isDisabledScroll={true}
                isFlexibleHeight={false}
                isFoldable
                panelHeaderProps={{
                  isStickyTop: true,
                  title:
                    `${ctIntl.formatMessage({
                      id: 'Status',
                    })} : `.toUpperCase() +
                    `${ctIntl.formatMessage({
                      id:
                        driverStatusLookup.find(
                          (option) => Number(option.id) === driver?.driverStatusId,
                        )?.description || '-',
                    })}`,
                  customActions: (
                    <>
                      {deliveryDriverMetricCapacity && (
                        <Box
                          {...makeSanitizedInnerHtmlProp({
                            dirtyHtml:
                              deliveryDriverMetricCapacity.isCapableToHandleLoad
                                ? DriverIsCapableIcon
                                : DriverIsInCapableIcon,
                          })}
                          width="14px"
                          height="14px"
                        />
                      )}
                    </>
                  ),
                }}
                onFoldClick={() => setIsFoldedStatusDetails((prev) => !prev)}
                isFolded={isFoldedStatusDetails}
              >
                <DriverStatusDetails
                  metricData={statusDetailsData}
                  routeInfoData={routeInfoDetailsData}
                />
              </Panel>
            )}

            <Panel
              isDisabledScroll={true}
              isFlexibleHeight={false}
              isFoldable
              panelHeaderProps={{
                isStickyTop: true,
                title: ctIntl.formatMessage(
                  {
                    id: 'ASSIGNED JOBS ({totalJobs})',
                  },
                  {
                    values: {
                      totalJobs: totalJobs,
                    },
                  },
                ),
              }}
              onFoldClick={() => setIsFoldedDriverStopDetails((prev) => !prev)}
              isFolded={isFoldedDriverStopDetails}
              nodeBetweenHeaderAndBody={
                currentTab === '1' &&
                isChangingJobStopsSequence && (
                  <ChangingSequenceWrapper>
                    <Box
                      display="flex"
                      gap="10px"
                    >
                      <Box padding="6px 0">
                        <ButtonSequenceWrapper boxShadow="0 2px 5px 0 rgba(0,0,0,0.06);">
                          <Button
                            variant="danger"
                            isCapsule
                            onClick={() => {
                              setJobStopInfo(defaultStops)
                              dispatch(stoppedChangingJobStopsSequence())
                            }}
                          >
                            {ctIntl.formatMessage({
                              id: 'Discard',
                            })}
                          </Button>
                        </ButtonSequenceWrapper>
                      </Box>
                      <Box
                        padding="6px 0"
                        width="100%"
                      >
                        <ButtonSequenceWrapper boxShadow="0 2px 5px 0 rgb(0 0 0 / 18%);">
                          <Button
                            variant="primary"
                            isCapsule
                            width="100%"
                            onClick={() => {
                              mutate({
                                deliveryDriverId: focusedDeliveryDriverIds[0] || '',
                                orderedStopIds: jobStopInfo.currentStops.map(
                                  (stop) => +stop.stopId,
                                ),
                              })
                            }}
                            loading={{ isLoading: isPending }}
                          >
                            {ctIntl.formatMessage({
                              id: 'Save Updates',
                            })}
                          </Button>
                        </ButtonSequenceWrapper>
                      </Box>
                    </Box>
                  </ChangingSequenceWrapper>
                )
              }
            >
              <Box ref={assignedJobsRef}>
                {jobStopInfo.stops.length > 0 &&
                  !deliveryDriverJobs.query.isPending && (
                    <Box padding="10px 20px 0">
                      <Tabs
                        activeTab={currentTab}
                        tabs={tabList}
                        onClick={(target: string) => setCurrentTab(target)}
                        activeColor={`var(--styleActiveButtonsColour)`}
                      />
                    </Box>
                  )}

                {jobStopInfo.currentStops.length > 0 && !isChangingJobStopsSequence && (
                  <RouteAndETASimulatorWrapper
                    disableOptimizeDriverStops={disableOptimizeDriverStops}
                  >
                    <RouteOptimizeButton
                      onClick={() => handleOptimizeButton()}
                      disabled={
                        optimizeDriverStopsRoute.isPending || disableOptimizeDriverStops
                      }
                      content={{
                        isLoading: optimizeDriverStopsRoute.isPending,
                        text: optimizeDriverStopsRoute.isPending ? (
                          ctIntl.formatMessage({
                            id: 'delivery.rightPanel.driver.optimizing',
                          })
                        ) : disableOptimizeDriverStops ? (
                          <Box color={`var(--styleActiveButtonsColour)`}>
                            {ctIntl.formatMessage({
                              id: 'delivery.rightPanel.driver.optimize',
                            })}
                          </Box>
                        ) : (
                          ctIntl.formatMessage({
                            id: 'delivery.rightPanel.driver.optimize',
                          })
                        ),
                      }}
                    />
                    {isShowOptimizationWarning && (
                      <Tooltip
                        title={ctIntl.formatMessage(
                          {
                            id: 'There are {picupLimit} or more assigned jobs on the list. System may encounter difficulty with a large amount of jobs.',
                          },
                          {
                            values: {
                              picupLimit: optimizationMaxLimit,
                            },
                          },
                        )}
                      >
                        <Box
                          margin="5px 0px"
                          padding="8px"
                          display="flex"
                          gap="10px"
                          alignItems="center"
                          fontSize="12px"
                          border="1px solid"
                          textAlign="left"
                          color={themeType.light.orange6}
                          borderColor={themeType.light.orange6}
                        >
                          <Icon icon="exclamation-triangle" />
                          <span>
                            {ctIntl.formatMessage(
                              {
                                id: 'Optimizing over {picupLimit} jobs may encounter difficulties',
                              },
                              {
                                values: {
                                  picupLimit: optimizationMaxLimit,
                                },
                              },
                            )}
                          </span>
                        </Box>
                      </Tooltip>
                    )}
                    {deliveryDriverJobs?.driverLocationETA && (
                      <ETASimulator
                        etaStatus={etaStatus}
                        driverLocationETA={deliveryDriverJobs?.driverLocationETA}
                        refreshIconOnClick={() => {
                          const stopIds = jobStopInfo.stops.map((stop) => +stop.stopId)
                          recalculateStopLegs.mutate({
                            deliveryDriverId: focusedDeliveryDriverIds[0],
                            orderedStopIds: stopIds,
                          })
                          setETASimulateValue(null)
                        }}
                        timepickerOnChange={(date) => {
                          setETASimulateValue(date)
                        }}
                      />
                    )}
                  </RouteAndETASimulatorWrapper>
                )}
                {currentTab === '1' ? (
                  <>
                    {!isChangingJobStopsSequence && (
                      <>
                        {jobStopInfo.stops.length > 0 &&
                          !deliveryDriverJobs.query.isPending && (
                            <>
                              <Box
                                display="flex"
                                padding="12px 20px 0 20px"
                                fontSize="12px"
                              >
                                <Box
                                  flexGrow={'1'}
                                  textAlign="left"
                                >
                                  <FormattedMessage
                                    id="Progress {completedStops}/{totalStops} stops"
                                    values={{
                                      completedStops:
                                        (deliveryDriverJobs &&
                                          deliveryDriverJobs.progressData &&
                                          deliveryDriverJobs.progressData
                                            .completedStops) ||
                                        0,
                                      totalStops:
                                        (deliveryDriverJobs &&
                                          deliveryDriverJobs.progressData &&
                                          deliveryDriverJobs.progressData.totalStops) ||
                                        0,
                                    }}
                                  />
                                </Box>
                                <Box>
                                  {(deliveryDriverJobs &&
                                    deliveryDriverJobs.progressData &&
                                    deliveryDriverJobs.progressData.jobProgressLabel) ||
                                    ''}
                                  {deliveryDriverJobs &&
                                    deliveryDriverJobs.progressData &&
                                    deliveryDriverJobs.progressData.totalStops &&
                                    deliveryDriverJobs.progressData.totalStops === 0 &&
                                    `${ctIntl.formatMessage(
                                      {
                                        id: '{percentage} % left',
                                      },
                                      {
                                        values: {
                                          percentage: 0,
                                        },
                                      },
                                    )}`}
                                </Box>
                              </Box>
                              <Box padding="6px 20px 0 20px">
                                <Progress
                                  fillColor={DELIVERY_COLOR.GREEN}
                                  completed={jobProgress}
                                  width="100%"
                                />
                              </Box>
                            </>
                          )}
                      </>
                    )}

                    <Box padding="16px 10px 0 20px">
                      {jobStopInfo.currentStops.length > 0 && (
                        <>
                          <CurrentStopStartLocation
                            driverStartLocationCustomerId={
                              driver?.startLocationCustomerId
                            }
                            driverEndLocationCustomerId={driver?.endLocationCustomerId}
                            driver={driver}
                            startStop={startStop}
                            endStop={endStop}
                            currentStops={jobStopInfo.currentStops}
                            legs={jobStopInfo.legs}
                            moveCard={moveCard}
                            startedChangingJobStopsSequenceHandler={
                              startedChangingJobStopsSequenceHandler
                            }
                            swapBreakingPoint={swapBreakingPoint}
                            isDraggingPickupStop={isDraggingPickupStop}
                          />
                        </>
                      )}

                      {jobStopInfo.futureStops.length > 0 && (
                        <>
                          <FutureStops
                            futureStops={jobStopInfo.futureStops}
                            deliveryDriverJobs={deliveryDriverJobs}
                          />
                        </>
                      )}

                      {jobStopInfo.stops.length === 0 &&
                        !deliveryDriverJobs.query.isPending && (
                          <RouteStatusWrapper ref={svgIconRef}>
                            <RouteDriverStatusSVG
                              {...makeSanitizedInnerHtmlProp({
                                dirtyHtml: DriverNoJobTodo,
                              })}
                            />
                            <RouterDriverText>
                              {ctIntl.formatMessage({
                                id: 'No job assigned',
                              })}
                            </RouterDriverText>
                          </RouteStatusWrapper>
                        )}
                    </Box>
                  </>
                ) : (
                  <Box padding="0 20px 10px">
                    {deliveryDriverJobs &&
                      deliveryDriverJobs.deduplicatedJobs.map((item) => (
                        <JobItemContainer
                          key={item.jobId}
                          onClick={() =>
                            dispatch(clickedOverviewJobDetail([item.jobId]))
                          }
                        >
                          <JobItem
                            job={{
                              jobData: {
                                ...item,
                              },
                              type: 'with-right-click-context',
                            }}
                            isActive={false}
                          />
                        </JobItemContainer>
                      ))}
                  </Box>
                )}
              </Box>
            </Panel>
            {!isFoldedDriverStopDetails &&
              driver?.isPlanning &&
              !deliveryDriverJobs.query.isPending && (
                <LockedStatusCard
                  desc={`${ctIntl.formatMessage({
                    id: 'Some actions might be limited during this time. This driver will be unlocked short after',
                  })}.`}
                />
              )}
          </SplitTopPart>
          {/*// DWA-3131 Disable Chat*/}
          {/*<SplitBottomPart>*/}
          {/*  <Panel*/}
          {/*    height="100%"*/}
          {/*    isFoldable*/}
          {/*    panelHeaderProps={{*/}
          {/*      title: (*/}
          {/*        <ChatPanelTitle*/}
          {/*          isUserTyping={isUserTyping}*/}
          {/*          isConnecting={isConnecting}*/}
          {/*          connection={connection}*/}
          {/*        />*/}
          {/*      ),*/}
          {/*      customActions:*/}
          {/*        unreadMessagesCount > 0 && isFoldedChat ? (*/}
          {/*          <MessageTip>*/}
          {/*            {unreadMessagesCount}{' '}*/}
          {/*            {ctIntl.formatMessage({ id: 'New Message' })}*/}
          {/*          </MessageTip>*/}
          {/*        ) : null,*/}
          {/*    }}*/}
          {/*    onHeaderClick={() => setIsFoldedChat((prev) => !prev)}*/}
          {/*    isFolded={isFoldedChat}*/}
          {/*  >*/}
          {/*    <Chat driver={driver} />*/}
          {/*  </Panel>*/}
          {/*</SplitBottomPart>*/}
        </SplitContainer>

        {(!driver || deliveryDriverJobs.query.isPending) && <LoadingOverlay />}
      </SplitContainerWrapper>
    </>
  )
}

export default DriverInfo
