import { useCallback, useContext, useEffect, useMemo, useState } from 'react'
import { isEmpty, orderBy } from 'lodash'
import { useQueryClient } from '@tanstack/react-query'
import { Draggable, Droppable } from 'react-beautiful-dnd'
import { useDispatch, useSelector } from 'react-redux'
import styled, { css } from 'styled-components'

import { getUsers } from 'duxs/admin'
import {
  deliveryGetDriversQueryKey,
  type FetchGetDeliveryDrivers,
} from 'src/modules/delivery/api/drivers/useDeliveryGetDrivers'
import type { FetchImportJobTemplate } from 'src/modules/delivery/api/import-job-template/useImportJobTemplateList'
import { parseMinimalJobData } from 'src/modules/delivery/api/jobs/helpers'
//api
import useDeliveryJobListsQuery, {
  type FetchDeliveryJobLists,
  type FetchDeliveryJobListsFilter,
} from 'src/modules/delivery/api/jobs/useDeliveryJobListsQuery'
import useGetLookQuery from 'src/modules/delivery/api/lookup/useGetLookupQuery'
import {
  deliveryPlanListsQueryKey,
  type FetchDeliveryPlanLists,
} from 'src/modules/delivery/api/plans/useDeliveryPlanListsQuery'
import Box from 'src/modules/delivery/components/Box'
import ContextMenu from 'src/modules/delivery/components/ContextMenu'
import DragLayers from 'src/modules/delivery/components/CustomDragAndDropPreview'
//components
import DeliveryList from 'src/modules/delivery/components/List'
//types
import type { OptionType } from 'src/modules/delivery/components/List/components/advance-search-dropdown'
import {
  JOB_STATUS_DROPDOWN_FILTERS_ICON,
  JOB_STATUS_DROPDOWN_FILTERS_ICON_COLOR,
  JOB_STATUS_DROPDOWN_FILTERS_OPTIONS,
} from 'src/modules/delivery/components/List/components/advance-search-dropdown/constants'
import EmptyListsWrapper from 'src/modules/delivery/components/List/components/empty-lists'
import LoadingOverlay from 'src/modules/delivery/components/Loading/PanelOverlay'
import { useMultiJobsContextItems } from 'src/modules/delivery/hooks/right-click-context/useMultiJobsContextItems'
import DeliveryImport from 'src/modules/delivery/import/job'
import { LeftPanelSplitContext } from 'src/modules/delivery/left-panel'
import { resetjobStopsBeingCreated } from 'src/modules/delivery/right-panel/Job/View/slice'
//slice
import {
  clickedLeftPaneDeliveryJobWithHotKey,
  clickedLeftPanelDeliveryCreateJob,
  clickedLeftPanelDeliveryJob,
  getFocusedDeliveryJobsIds,
  getOpenModal,
  multipleSelectDeliveryJobIdsWithShiftKey,
  selectedActiveImport,
} from 'src/modules/delivery/slice'
//styled
import { StatusIcon } from 'src/modules/delivery/styled/GlobalStyles'
//constants
import {
  DND_TYPES,
  IMPORT_TYPE,
  JOB_STATUS_TO_ID,
  MODAL_TYPES,
  SPLIT_BOTTOM_MINIMUM_PERCENTAGE,
} from 'src/modules/delivery/utils/constants'
import { getJobStatusDescription } from 'src/modules/delivery/utils/helpers'
import { generateItemMatchesWithTextAndFilters } from 'src/modules/delivery/utils/search-utils'
import { useTypedSelector } from 'src/redux-hooks'
import type { ValueOf } from 'src/types'
//helpers
import { ctIntl } from 'src/util-components/ctIntl'
import type { Filters } from 'src/util-functions/search-utils'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

//svg
import AddNewJobSVG from 'assets/svg/Delivery/add-new-job.svg'

import ListsHeader from '../lists-header'
import {
  JOB_FILTER_BUTTON_ID,
  JOB_FILTER_BUTTON_PRESELECTED_STATUS,
  JOB_FILTER_BUTTONS,
} from './constants'
// import DraggableJobItem from './Item/DraggableItem'
import JobItem from './Item/ItemView'
import { MenuButton } from './Item/styles'

const normalizeText = (text: string | undefined | null): string => {
  if (!text) return ''
  return text.normalize('NFD').replace(/[\u0300-\u036F]/g, '')
}

type Props = {
  jobs: FetchDeliveryJobLists.Return
  isHotKeyPressed: boolean
  queryStatus: 'idle' | 'pending' | 'success' | 'error'
  templates: Array<FetchImportJobTemplate.Return> | undefined
}

export function useCalcDndJobIds() {
  const focusedDeliveryJobsIds = useSelector(getFocusedDeliveryJobsIds)
  const queryClient = useQueryClient()
  const jobList = queryClient.getQueryData<FetchDeliveryJobListsFilter.Return>(
    // eslint-disable-next-line react-hooks/react-compiler
    useDeliveryJobListsQuery.createKey(),
  )

  return useCallback(
    (draggingJobId: number) =>
      focusedDeliveryJobsIds.includes(draggingJobId)
        ? jobList
            ?.filter(
              (job) => focusedDeliveryJobsIds.includes(job.jobId) && job.canReassign,
            )
            .map((job) => job.jobId) || []
        : [draggingJobId],
    [focusedDeliveryJobsIds, jobList],
  )
}

const JobsList = ({ jobs, isHotKeyPressed, queryStatus, templates }: Props) => {
  const users = useTypedSelector(getUsers)
  const focusedDeliveryJobsIds = useSelector(getFocusedDeliveryJobsIds)
  const queryClient = useQueryClient()
  const dispatch = useDispatch()
  const lookup = useGetLookQuery()
  const [showModal, setShowModal] = useState(false)
  const [quickButtonFilter, setQuickButtonFilter] = useState<ValueOf<
    typeof JOB_FILTER_BUTTON_ID
  > | null>(null)
  const [searchSelectFilter, setSearchSelectFilter] = useState<
    Array<OptionType> | undefined
  >(undefined)
  const openModal = useSelector(getOpenModal)
  const plansList = queryClient.getQueryData<FetchDeliveryPlanLists.Return>(
    deliveryPlanListsQueryKey(),
  )
  const driversList = queryClient.getQueryData<FetchGetDeliveryDrivers.Return>(
    deliveryGetDriversQueryKey(),
  )

  const scheduleTypeOptions = useMemo(
    () => (lookup.status === 'success' ? lookup.data.scheduleTypeOptions : []),
    [lookup],
  )

  const jobStatusTranslatedFilterOptions = JOB_STATUS_DROPDOWN_FILTERS_OPTIONS.map(
    (option) => ({
      ...option,
      label: ctIntl.formatMessage({ id: option.label }),
    }),
  )

  const parsedJobs = jobs.map((job) => ({
    ...job,
    scheduleType: {
      ...job.scheduleType,
      description:
        scheduleTypeOptions.find(
          (option) => Number(option.value) === job.scheduleTypeId,
        )?.label || '',
    },
    jobStatusDescription:
      getJobStatusDescription(job.deliveryDriverId, job.planId, job.jobStatusId) || '',
  }))

  const splitContext = useContext(LeftPanelSplitContext)
  const filteredJobs = useMemo(() => {
    const searchFilters: Filters<(typeof parsedJobs)[number]> = {
      search: [
        (job) => job.referenceNumber || job.orderId,
        (job) => `${job.driver?.firstName} ${job.driver?.lastName}`,
        (job) => job.scheduleType.description,
        (job) => job.trackingNumber,
        (job) => job.planName,
        (job) => users.find((user) => user.id === job.subuserId)?.username || '',
        (job) => {
          const driverId = plansList?.find((plan) => plan.planId === job.planId)
            ?.targetDriverId
          return driversList?.find((driver) => driver.driverId === driverId)?.fullName
        },
      ],
    }

    const statusArray = searchSelectFilter
      ?.filter((status) => typeof status.value === 'number')
      .map((status) => ctIntl.formatMessage({ id: status.label }))

    const keywordArray: Array<string> = []
    const keyword = searchSelectFilter?.filter(
      (status) => typeof status.value !== 'number',
    )

    const filteredJobs: Array<FetchDeliveryJobLists.Return> = []

    const statusFilteredJobs =
      statusArray && statusArray.length > 0
        ? parsedJobs.filter((job) =>
            statusArray.includes(
              ctIntl.formatMessage({ id: job.jobStatusDescription }),
            ),
          )
        : parsedJobs

    //Added scheduled attribute it's own filter to avoid collision with status
    const scheduledFilteredJobs =
      statusArray && statusArray.length > 0
        ? parsedJobs.filter((job) =>
            statusArray.includes(
              ctIntl.formatMessage({
                id: job.scheduledDescription.statusDescription,
              }),
            ),
          )
        : parsedJobs

    const mergeFilteredJob = [...statusFilteredJobs, ...scheduledFilteredJobs]
    const forFilteredJobs = isEmpty(mergeFilteredJob)
      ? []
      : [...new Set(mergeFilteredJob.flat())]
    if (keyword && keyword.length > 0) {
      for (const k of keyword) {
        keywordArray.push(k.label)
      }

      for (const keyword of keywordArray)
        filteredJobs.push(
          forFilteredJobs.filter((job) => {
            const { itemMatchesWithTextAndFilters } =
              generateItemMatchesWithTextAndFilters(normalizeText(keyword))

            const normalizedJob = {
              ...job,
              referenceNumber: normalizeText(job.referenceNumber),
              orderId: normalizeText(job.orderId),
              driver: job.driver
                ? {
                    ...job.driver,
                    firstName: normalizeText(job.driver.firstName),
                    lastName: normalizeText(job.driver.lastName),
                  }
                : job.driver,
              scheduleType: {
                ...job.scheduleType,
                description: normalizeText(job.scheduleType.description),
              },
              trackingNumber: normalizeText(job.trackingNumber),
              planName: normalizeText(job.planName),
            }

            return itemMatchesWithTextAndFilters(normalizedJob, searchFilters)
          }),
        )
    }

    return isEmpty(filteredJobs) ? forFilteredJobs : [...new Set(filteredJobs.flat())]
  }, [driversList, parsedJobs, plansList, searchSelectFilter, users])

  //Sort to original order by jobStatusId followed by the latest created jobId
  const sortedFilteredJobs = orderBy(
    filteredJobs,
    [(item) => item.jobListsOrder, (item) => item.jobStatusId, (item) => item.jobId],
    ['asc', 'asc', 'desc'],
  )

  useEffect(() => {
    const statusFilterValue =
      searchSelectFilter && !isEmpty(searchSelectFilter)
        ? searchSelectFilter.map((status) => status.value)
        : null
    const isUndone =
      statusFilterValue &&
      JOB_FILTER_BUTTON_PRESELECTED_STATUS[JOB_FILTER_BUTTON_ID.UNDONE]
        .map((status) => status.value)
        .every((filterOption) => statusFilterValue.includes(filterOption))
    const isUnassigned =
      statusFilterValue &&
      JOB_FILTER_BUTTON_PRESELECTED_STATUS[JOB_FILTER_BUTTON_ID.UNASSIGNED]
        .map((status) => status.value)
        .every((filterOption) => statusFilterValue.includes(filterOption))

    setQuickButtonFilter(null)

    if (isUnassigned) {
      setQuickButtonFilter(JOB_FILTER_BUTTON_ID.UNASSIGNED)
    }

    if (isUndone) {
      setQuickButtonFilter(JOB_FILTER_BUTTON_ID.UNDONE)
    }
  }, [searchSelectFilter])

  const handleItemOnClick = (
    id: number,
    index: number,
    altKey: boolean,
    cmdKey: boolean,
    shiftKey: boolean,
  ) => {
    if (isHotKeyPressed) {
      if ((altKey || cmdKey) && !shiftKey) {
        return dispatch(clickedLeftPaneDeliveryJobWithHotKey(id))
      }
      if (shiftKey && !altKey && !cmdKey) {
        if (focusedDeliveryJobsIds.length === 0) {
          return dispatch(clickedLeftPaneDeliveryJobWithHotKey(id))
        } else {
          const lastSelectedIndex = sortedFilteredJobs.findIndex(
            (job) => job.jobId === focusedDeliveryJobsIds.at(-1),
          )
          const result: Array<number> = []
          const [smallerIndex, biggerIndex] = [
            Math.min(index, lastSelectedIndex),
            Math.max(index, lastSelectedIndex),
          ]
          for (let i = smallerIndex; i <= biggerIndex; i++) {
            if (sortedFilteredJobs[i]) {
              result.push(sortedFilteredJobs[i].jobId)
            }
          }
          // Keep the first shift-selected item in the last of focusedDeliveryJobsIds array
          if (index > lastSelectedIndex) result.reverse()
          return dispatch(multipleSelectDeliveryJobIdsWithShiftKey(result))
        }
      }
    }
    dispatch(resetjobStopsBeingCreated())
    return dispatch(clickedLeftPanelDeliveryJob(id))
  }

  const handleCreateJobClick = () => {
    dispatch(resetjobStopsBeingCreated())
    dispatch(clickedLeftPanelDeliveryCreateJob())
  }

  const handleImportJobClick = () => {
    setShowModal(true)
    dispatch(selectedActiveImport(IMPORT_TYPE.JOB))
  }

  const renderLoadingIndicator = queryStatus === 'pending'

  const handleSearchHeaderOnChange = (value: Array<OptionType>) =>
    setSearchSelectFilter(value)

  const customOptions = (optionProps: Record<string, any>) => (
    <Box display="flex">
      <StatusIcon
        {...makeSanitizedInnerHtmlProp({
          dirtyHtml:
            JOB_STATUS_DROPDOWN_FILTERS_ICON[
              optionProps.value as keyof typeof JOB_STATUS_DROPDOWN_FILTERS_ICON
            ],
        })}
        color={
          JOB_STATUS_DROPDOWN_FILTERS_ICON_COLOR[
            optionProps.value as keyof typeof JOB_STATUS_DROPDOWN_FILTERS_ICON_COLOR
          ] || ''
        }
      />
      <Box>{optionProps.label}</Box>
    </Box>
  )

  const handleQuickFilterClick = (id: ValueOf<typeof JOB_FILTER_BUTTON_ID>) => {
    setSearchSelectFilter(
      JOB_FILTER_BUTTON_PRESELECTED_STATUS[id].map((option) => ({
        ...option,
        label: ctIntl.formatMessage({ id: option.label }),
      })),
    )
    setQuickButtonFilter(id)
  }

  const jobFilterCustomMenu = (
    <Box
      display="flex"
      justifyContent="space-evenly"
      margin="5px 10px"
    >
      {JOB_FILTER_BUTTONS.map((filterButtons) => (
        <MenuButton
          key={filterButtons.value}
          variant="default"
          onClick={() => handleQuickFilterClick(filterButtons.value)}
          isActive={filterButtons.value === quickButtonFilter}
        >
          {ctIntl.formatMessage({ id: filterButtons.label })}
        </MenuButton>
      ))}
    </Box>
  )

  const emptyListsComponent =
    queryStatus !== 'pending' &&
    !splitContext.splitResize.topIsFolded &&
    sortedFilteredJobs.length === 0 ? (
      <EmptyListsWrapper
        title="Click to add new"
        svgIcon={AddNewJobSVG}
      />
    ) : undefined

  const jobList = queryClient.getQueryData<FetchDeliveryJobListsFilter.Return>(
    // eslint-disable-next-line react-hooks/react-compiler
    useDeliveryJobListsQuery.createKey(),
  )

  const { multiContextItems } = useMultiJobsContextItems({
    jobIds: focusedDeliveryJobsIds,
    jobList: jobList ? jobList.map((jobs) => ({ ...parseMinimalJobData(jobs) })) : [],
  })

  return (
    <Box position="relative">
      {showModal && templates && (
        <DeliveryImport
          showModal={showModal}
          setShowModal={() => setShowModal(!showModal)}
          templates={templates}
        />
      )}
      <ListsHeader
        listHeaderDetails={{
          listHeaderName: ctIntl.formatMessage({ id: 'Jobs' }),
          listHeaderCount: sortedFilteredJobs.length,
        }}
        advanceSearchProps={{
          onSelectSearchFilterChange: handleSearchHeaderOnChange,
          searchOptions: jobStatusTranslatedFilterOptions,
          searchCustomOptions: customOptions,
          searchFilterPlaceholder: ctIntl.formatMessage({ id: 'Search/filter jobs' }),
          searchCustomMenu: jobFilterCustomMenu,
        }}
        isFolded={splitContext.splitResize.topIsFolded}
        onSearchSelectValue={searchSelectFilter}
        onCloseSearch={() => {
          setSearchSelectFilter([])
          setQuickButtonFilter(null)
        }}
        onAddButtonClick={() => handleCreateJobClick()}
        onImportButtonClick={() => handleImportJobClick()}
        addLabel="Add New Job"
        onFoldClick={() => {
          splitContext.setSplitResize({
            ...splitContext.splitResize,
            topIsFolded: !splitContext.splitResize.topIsFolded,
            topSplitResize: ((topFolded, BottomFolded) => {
              if (!topFolded && !BottomFolded) {
                return SPLIT_BOTTOM_MINIMUM_PERCENTAGE
              }

              if (!topFolded && BottomFolded) {
                return splitContext.splitResize.prevTopSplitResize
              }

              if (topFolded && BottomFolded) {
                return SPLIT_BOTTOM_MINIMUM_PERCENTAGE
              }

              return splitContext.splitResize.prevTopSplitResize
            })(
              splitContext.splitResize.topIsFolded,
              splitContext.splitResize.bottomIsFolded,
            ),
            bottomSplitResize: splitContext.splitResize.topIsFolded
              ? splitContext.splitResize.prevBottomSplitResize
              : SPLIT_BOTTOM_MINIMUM_PERCENTAGE,
          })
        }}
        isAdvanceSearch
      />
      <DragLayers jobs={sortedFilteredJobs} />
      {renderLoadingIndicator && <LoadingOverlay />}
      <Droppable
        type={DND_TYPES.ASSIGN_LEFT_PANEL_JOBS}
        isDropDisabled
        droppableId="left-panel-jobs-dnd"
        mode="virtual"
        renderClone={(provided, _, rubric) => {
          const draggingJob = sortedFilteredJobs[rubric.source.index]
          const selectedJobs = focusedDeliveryJobsIds.includes(draggingJob.jobId)
            ? parsedJobs.filter(
                (job) => focusedDeliveryJobsIds.includes(job.jobId) && job.canReassign,
              )
            : [draggingJob]

          return (
            <div
              {...provided.draggableProps}
              {...provided.dragHandleProps}
              ref={provided.innerRef}
            >
              {selectedJobs.map((job) => (
                <JobItem
                  key={job.jobId}
                  job={{
                    jobData: job,
                    type: 'without-right-click-context',
                  }}
                  style={{
                    height: 40,
                    border: '1px solid rgba(0, 0, 0, 0.05)',
                    background: 'white',
                    opacity: '.8',
                  }}
                />
              ))}
            </div>
          )
        }}
      >
        {(provided, snapshot) => (
          <DeliveryList
            // defaultSplitHeaderHeight 30px
            padding={`0 !important`}
            height={splitContext.splitResize.topSplitResize}
            rowCount={sortedFilteredJobs.length}
            isFolded={splitContext.splitResize.topIsFolded}
            listsPanelOnClick={
              sortedFilteredJobs.length === 0 ? handleCreateJobClick : undefined
            }
            emptylistsNode={emptyListsComponent}
            heightOffset={32}
            innerRef={provided.innerRef}
          >
            {({ index, style, key }) => {
              const job = sortedFilteredJobs[index]
              const jobId = job.jobId
              const isTabActive = focusedDeliveryJobsIds.includes(jobId)
              const isNotSelectable =
                isHotKeyPressed &&
                JOB_STATUS_TO_ID.ASSIGNED === job.jobStatusId &&
                !job.canReassign
              const isIncludedInDraggingJobs =
                snapshot.draggingFromThisWith &&
                job.canReassign &&
                focusedDeliveryJobsIds.includes(job.jobId) &&
                focusedDeliveryJobsIds.includes(Number(snapshot.draggingFromThisWith))

              function renderJobItem() {
                return (
                  <Draggable
                    key={job.jobId}
                    draggableId={String(job.jobId)}
                    index={index}
                    isDragDisabled={!job.canReassign}
                  >
                    {(provided) => (
                      <div
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        ref={provided.innerRef}
                        style={{
                          ...style,
                          cursor: 'pointer',
                          opacity: isIncludedInDraggingJobs ? '0' : 'initial',
                        }}
                      >
                        {focusedDeliveryJobsIds.length > 1 ? (
                          <ContextMenu popoverItems={multiContextItems}>
                            <JobItem
                              job={{
                                jobData: job,
                                type: 'without-right-click-context',
                              }}
                              style={{
                                height: style.height,
                                width: style.width,
                              }}
                              isActive={isTabActive}
                              isLoading={job.assigning}
                              isSelected={isTabActive}
                              withDivider
                            />
                          </ContextMenu>
                        ) : (
                          <JobItem
                            job={{ jobData: job, type: 'with-right-click-context' }}
                            style={{
                              height: style.height,
                              width: style.width,
                            }}
                            isActive={isTabActive}
                            isLoading={job.assigning}
                            isSelected={isTabActive}
                            withDivider
                          />
                        )}
                      </div>
                    )}
                  </Draggable>
                )
                // if (job.canReassign) {
                //   return focusedDeliveryJobsIds.length > 1 ? (
                //     <ContextMenu popoverItems={multiContextItems}>
                //       <DraggableJobItem
                //         job={{ jobData: job, type: 'without-right-click-context' }}
                //         style={style}
                //         isActive={isTabActive}
                //         isLoading={job.assigning}
                //         isSelected={isTabActive}
                //         withDivider
                //       />
                //     </ContextMenu>
                //   ) : (
                //     <Draggable
                //       key={job.jobId}
                //       draggableId={String(job.jobId)}
                //       index={index}
                //     >
                //       {(provided, snapshot) => (
                //         <div
                //           {...provided.draggableProps}
                //           {...provided.dragHandleProps}
                //           ref={provided.innerRef}
                //           style={{
                //             ...style,
                //             cursor: 'pointer',
                //             background: snapshot.isDragging ? 'white' : 'inherit',
                //           }}
                //         >
                //           <JobItem
                //             job={{ jobData: job, type: 'with-right-click-context' }}
                //             style={{
                //               height: style.height,
                //               width: style.width,
                //             }}
                //             isActive={isTabActive}
                //             isLoading={job.assigning}
                //             isSelected={isTabActive}
                //             withDivider
                //           />
                //         </div>
                //       )}
                //     </Draggable>
                //     // <DraggableJobItem
                //     //   job={{ jobData: job, type: 'with-right-click-context' }}
                //     //   style={style}
                //     //   isActive={isTabActive}
                //     //   isLoading={job.assigning}
                //     //   isSelected={isTabActive}
                //     //   withDivider
                //     // />
                //   )
                // } else {
                //   return focusedDeliveryJobsIds.length > 1 ? (
                //     <ContextMenu popoverItems={multiContextItems}>
                //       <JobItem
                //         job={{ jobData: job, type: 'without-right-click-context' }}
                //         style={style}
                //         isActive={isTabActive}
                //         isLoading={job.assigning}
                //         isSelected={isTabActive}
                //         withDivider
                //       />
                //     </ContextMenu>
                //   ) : (
                //     <JobItem
                //       job={{ jobData: job, type: 'with-right-click-context' }}
                //       style={style}
                //       isActive={isTabActive}
                //       isLoading={job.assigning}
                //       isSelected={isTabActive}
                //       withDivider
                //     />
                //   )
                // }
              }

              return (
                <ListWrapper
                  isActive={isTabActive}
                  key={key}
                  onClick={(e) =>
                    isNotSelectable || openModal === MODAL_TYPES.JOB_DELETE_MODAL
                      ? undefined
                      : handleItemOnClick(
                          jobId,
                          index,
                          e.ctrlKey,
                          e.metaKey,
                          e.shiftKey,
                        )
                  }
                  isSelectable={!isNotSelectable}
                >
                  {renderJobItem()}
                </ListWrapper>
              )
            }}
          </DeliveryList>
        )}
      </Droppable>
    </Box>
  )
}

export default JobsList

const ListWrapper = styled.div<{ isActive: boolean; isSelectable?: boolean }>`
  cursor: ${(p) => (p.isSelectable ? 'pointer' : 'not-allowed')};
  opacity: ${(p) => (p.isSelectable ? 1 : 0.5)};
  ${(p) =>
    p.isActive
      ? css`
          .item-wrapper {
            background-color: ${`var(--styleActiveButtonsColour)`};
          }
        `
      : ''};
`
