import { useMutation, useQueryClient } from '@tanstack/react-query'
import * as R from 'remeda'

import { apiCallerNoX } from 'src/api/api-caller'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import { ctIntl } from 'src/util-components/ctIntl'

import type { FileItem } from '../../appointment/components/CreateAppointmentModal/components/AppointmentFileUploader'

type FilePayload =
  | { file_name: string; base64_data: string }
  | { file_name: string; appointment_file_id: number }

type BaseAppointmentParams = {
  windowStartTime: string | null
  windowEndTime: string | null
  durationInMinutes: number
  properties: Record<string, any>
  capabilityId: number
  appointmentId: number
}

export declare namespace UpdateAppointment {
  type Params = BaseAppointmentParams & {
    files: Array<FileItem>
  }
  type Payload = BaseAppointmentParams & {
    files: Array<FilePayload>
  }
  type ApiOutput = {
    data: {
      id: string
      success: boolean
    }
  }
  type Return = {
    id: string
    success: boolean
  }
}

const convertFileToBase64 = (file: File): Promise<string> =>
  new Promise((resolve, reject) => {
    const fileReader = new FileReader()
    fileReader.readAsDataURL(file)
    fileReader.onload = () => resolve(fileReader.result as string)
    fileReader.onerror = (error) => reject(error)
  })

export default function useUpdateAppointmentMutation() {
  const queryClient = useQueryClient()

  return useMutation<UpdateAppointment.Return, Error, UpdateAppointment.Params>({
    mutationFn: async (appointmentParams) => {
      let processedFiles: Array<FilePayload> = []

      if (appointmentParams.files && R.isArray(appointmentParams.files)) {
        const uploadedFiles = appointmentParams.files

        const convertedFiles = await Promise.all(
          uploadedFiles.map(async (fileItem) => {
            if (fileItem instanceof File) {
              const base64Content = await convertFileToBase64(fileItem)
              return {
                file_name: fileItem.name,
                base64_data: base64Content,
              }
            }

            if ('appointmentFileId' in fileItem) {
              return {
                file_name: fileItem.name,
                appointment_file_id: fileItem.appointmentFileId,
              }
            }

            return fileItem
          }),
        )

        processedFiles = convertedFiles
      }

      const apiPayload: UpdateAppointment.Payload = {
        ...appointmentParams,
        files: processedFiles,
      }

      const response = await apiCallerNoX<UpdateAppointment.ApiOutput>(
        'delivery_update_appointment',
        {
          data: apiPayload,
        },
      )

      return response.data
    },
    onSuccess: (_data, variables, _context) => {
      queryClient.invalidateQueries({ queryKey: ['deliveryAppointment/tableList'] })

      if (variables && variables.appointmentId) {
        queryClient.invalidateQueries({
          queryKey: ['deliveryAppointment/details', variables.appointmentId],
        })
      }

      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Appointment updated',
        }),
        { variant: 'success' },
      )
    },
    onError: () => {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Failed to update appointment',
        }),
        { variant: 'error' },
      )
    },
  })
}
