import { useQuery } from '@tanstack/react-query'

import { apiCallerNoX } from 'api/api-caller'
import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import { createQuery } from 'src/util-functions/react-query-utils'

export type Appointment = {
  appointmentId: number
  updatedTs: string
  capabilityName: string
  customerName: string
  windowStartTime: string
  durationInMinutes: string
  patientCount: number
  jobStatus: string
  jobId: string
  bookingNumber: string
}

export declare namespace FetchAppointmentTableListQuery {
  type ApiInput = {
    filters: Array<{
      field: string
      operator: string
      value: string
      type: 'dateTime' | 'string' | 'number'
      from: string | undefined
      to: string | undefined
    }>
    pagination: {
      page: number
      perPage: number
    }
  }
  type ApiOutput = {
    data: {
      appointments: Array<Appointment>
      meta: {
        currentPage: number
        perPage: number
        totalPages: number
        totalItems: number
      }
    }
  }
  type Return = ReturnType<typeof parseAppointmentDetails>
}

const createKey = (apiInput?: FetchAppointmentTableListQuery.ApiInput) =>
  ['deliveryAppointment/tableList', ...(apiInput ? [apiInput] : [])] as const

const useAppointmentTableListQuery = (
  apiInput?: FetchAppointmentTableListQuery.ApiInput,
) =>
  useQuery({
    ...appointmentTableListQuery(apiInput),
  })

export const appointmentTableListQuery = (
  apiInput?: FetchAppointmentTableListQuery.ApiInput,
) =>
  createQuery({
    queryKey: createKey(apiInput),
    queryFn: () => fetchAppointmentTableList(apiInput),
    ...makeQueryErrorHandlerWithToast(),
  })

const fetchAppointmentTableList = (
  apiInput?: FetchAppointmentTableListQuery.ApiInput,
) =>
  apiCallerNoX<FetchAppointmentTableListQuery.ApiOutput>('delivery_get_appointments', {
    data: {
      pagination: apiInput?.pagination,
      filters: apiInput?.filters,
    },
  }).then((res) => parseAppointmentDetails(res.data))

export const parseAppointmentDetails = (
  res: FetchAppointmentTableListQuery.ApiOutput['data'],
) => ({
  meta: res.meta,
  appointments: res.appointments.map((item) => ({
    appointmentId: Number(item.appointmentId),
    updatedTs: item.updatedTs,
    capabilityName: item.capabilityName,
    customerName: item.customerName,
    windowStartTime: item.windowStartTime,
    durationInMinutes: item.durationInMinutes,
    patientCount: item.patientCount,
    jobStatus: item.jobStatus,
    jobId: item.jobId,
    bookingNumber: item.bookingNumber,
  })),
})

export default Object.assign(useAppointmentTableListQuery, {
  createKey,
})
