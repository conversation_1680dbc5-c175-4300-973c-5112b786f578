import { useQuery } from '@tanstack/react-query'

import { apiCallerNoX } from 'api/api-caller'
import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import { ctIntl } from 'src/util-components/ctIntl'
import { createQuery } from 'src/util-functions/react-query-utils'

import type { Appointment } from './useAppointmentTableListQuery'

export declare namespace FetchAppointmentTableColumnListQuery {
  type ApiOutput = {
    columns: Array<{
      headerName: string
      field: keyof Appointment
      type: 'string' | 'number' | 'dateTime'
      width: number
      format?: 'dateTime' | 'time' | 'duration' | string
      filterable?: boolean
      visible: boolean
    }>
  }
  type Return = ReturnType<typeof parseAppointmentTableColumns>
}

const createKey = () => ['deliveryAppointment/tableColumnList'] as const

const useAppointmentTableColumnListQuery = () =>
  useQuery({
    ...appointmentTableColumnListQuery(),
  })

export const appointmentTableColumnListQuery = () =>
  createQuery({
    queryKey: createKey(),
    queryFn: () => fetchAppointmentTableColumnList(),
    ...makeQueryErrorHandlerWithToast(),
  })

const fetchAppointmentTableColumnList = () =>
  apiCallerNoX<FetchAppointmentTableColumnListQuery.ApiOutput>(
    'delivery_get_appointment_table_columns',
    {
      data: {},
    },
  ).then((res) => parseAppointmentTableColumns(res))

const parseAppointmentTableColumns = (
  res: FetchAppointmentTableColumnListQuery.ApiOutput,
) =>
  res.columns.map((column) => ({
    ...column,
    headerName: ctIntl.formatMessage({ id: column.headerName }),
  }))

export default Object.assign(useAppointmentTableColumnListQuery, {
  createKey,
})
