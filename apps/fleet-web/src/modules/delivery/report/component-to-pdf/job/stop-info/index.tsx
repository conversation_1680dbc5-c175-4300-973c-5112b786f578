import { isEmpty } from 'lodash'
import { Text, View } from '@react-pdf/renderer'
import { format } from 'date-fns'
import moment from 'moment-timezone'

//theme
import { themeType } from 'src/modules/delivery/components/Theme'
import { formatShiftTime } from 'src/modules/delivery/right-panel/Driver/form'
import { JOB_STOP_STATUS_TO_ID } from 'src/modules/delivery/utils/constants'
//helper
import { ctIntl } from 'src/util-components/ctIntl'

//components
import Card from '../../commons/card'
//styles
import { styles } from '../../style'
//types
import type { JobReportProps } from '../index'
import StopTodos from '../todos/stops'
//constants
import { JOB_REPORT_STOP_STATUS_STOP_COLOR, JOB_STOP_STATUS_LABEL } from './constants'
import { DefaultStopStatusIcon } from './helpers'

const StopInfo = ({ data }: { data: JobReportProps }) => {
  const lastStop = data.stop[data.stop.length - 1]

  return (
    <>
      <Card
        borderColor={themeType.light.grey1}
        header={
          <Text
            style={{
              margin: '0 0 10px 5px',
              textTransform: 'uppercase',
              fontSize: '12px',
              fontWeight: 'bold',
              color: themeType.light.grey3,
            }}
          >
            {ctIntl.formatMessage({ id: 'Stop Info' })}
          </Text>
        }
        title={
          <View
            style={[
              styles.flex,
              {
                padding: '6px 8px',
                backgroundColor: `${themeType.light.grey24}`,
                borderTopLeftRadius: '7px',
                borderTopRightRadius: '7px',
                border: `1px solid ${themeType.light.grey1}`,
                borderBottom: 'transparent',
                fontSize: '13px',
                fontWeight: 'bold',
              },
            ]}
          >
            <View style={[styles.flex, { width: 'auto' }]}>
              {DefaultStopStatusIcon(lastStop.stopTypeId, lastStop.stopStatusId)}
              <Text
                style={{
                  marginLeft: '5px',
                  color: themeType.light.black,
                }}
              >
                {lastStop.customerName}
              </Text>
            </View>
            <Text
              style={{
                color:
                  lastStop.stopStatusId === JOB_STOP_STATUS_TO_ID.CREATED
                    ? themeType.light.grey11
                    : JOB_REPORT_STOP_STATUS_STOP_COLOR[
                        lastStop.stopStatusId as keyof typeof JOB_REPORT_STOP_STATUS_STOP_COLOR
                      ],
              }}
            >
              {ctIntl
                .formatMessage({ id: JOB_STOP_STATUS_LABEL[lastStop.stopStatusId] })
                .toUpperCase()}
            </Text>
          </View>
        }
        body={
          <>
            <View
              style={[styles.flex, { padding: '7px 15px', alignItems: 'flex-start' }]}
            >
              <View>
                <Text style={{ marginBottom: '8px', display: 'flex' }}>
                  <Text style={{ fontWeight: 'medium' }}>
                    {ctIntl.formatMessage({ id: 'Arrived' })}:{' '}
                  </Text>
                  <Text>
                    {lastStop.jobArrived &&
                      moment(lastStop.jobArrived).format('YYYY-MM-DD hh:mm A')}
                  </Text>
                </Text>
                <Text style={{ marginBottom: '8px', display: 'flex' }}>
                  <Text style={{ fontWeight: 'medium' }}>
                    {ctIntl.formatMessage({ id: 'Duration' })}:{' '}
                  </Text>
                  {lastStop.duration && (
                    <Text>
                      {lastStop.jobDuration}
                      {lastStop.jobDuration && lastStop.jobDuration > 1
                        ? 'mins'
                        : 'min'}
                    </Text>
                  )}
                </Text>
                <Text style={{ marginBottom: '8px', display: 'flex' }}>
                  <Text style={{ fontWeight: 'medium' }}>
                    {ctIntl.formatMessage({ id: 'Departure' })}:{' '}
                  </Text>
                  <Text>
                    {lastStop.jobCompleted &&
                      moment(lastStop.jobCompleted).format('YYYY-MM-DD hh:mm A')}
                  </Text>
                </Text>
                <Text style={{ marginBottom: '8px', display: 'flex' }}>
                  <Text style={{ fontWeight: 'medium' }}>
                    {ctIntl.formatMessage({ id: 'Time Window' })}:{' '}
                  </Text>
                  <Text>
                    {lastStop.deliveryWindows &&
                      format(
                        formatShiftTime(lastStop.deliveryWindows[0]?.timeFrom) ||
                          new Date(),
                        'hh:mm a',
                      )}{' '}
                    {lastStop.deliveryWindows &&
                      ` - ${format(
                        formatShiftTime(lastStop.deliveryWindows[0]?.timeTo) ||
                          new Date(),
                        'hh:mm a',
                      )}`}
                  </Text>
                </Text>
              </View>
              <View
                style={{
                  textAlign: 'right',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'flex-start',
                  alignItems: 'flex-end',
                  width: '50%',
                }}
              >
                {lastStop.addressLine1 && (
                  <Text style={{ marginBottom: '8px' }}>{lastStop.addressLine1}</Text>
                )}
                {lastStop.addressLine2 && (
                  <Text style={{ marginBottom: '8px' }}>{lastStop.addressLine2}</Text>
                )}
                {(lastStop.contactCode || lastStop.contactNumber) && (
                  <Text style={{ marginBottom: '8px' }}>
                    {`+${lastStop.contactCode}${lastStop.contactNumber}`}
                  </Text>
                )}
                <Text style={{ marginBottom: '8px' }}>{lastStop.email}</Text>
              </View>
            </View>
            {lastStop.stopTodo && !isEmpty(lastStop.stopTodo) ? (
              <View style={{ padding: '7px 15px' }}>
                <StopTodos
                  stopTodos={lastStop.stopTodo}
                  options={{
                    pdfTodoTypes: data.pdfTodoTypes,
                    pdfTodoStatuses: data.pdfTodoStatuses,
                  }}
                  layoutPOD={data.layoutPOD}
                />
              </View>
            ) : (
              <></>
            )}
          </>
        }
      />
    </>
  )
}

export default StopInfo
