import type { FetchAppointmentDetails } from '../api/appointment/useGetAppointmentDetailsQuery'
import type { FileItem } from './components/CreateAppointmentModal/components/AppointmentFileUploader'

/**
 * Converts server-side file objects to an array of objects that can be displayed in the UI
 * These objects can be used in the file list but aren't actual File objects
 */
export const convertServerFilesToDisplayFiles = (
  files?: Array<FetchAppointmentDetails.File>,
) => {
  if (!files || files.length === 0) return []

  return files.map((serverFile) => ({
    name: serverFile.file_name,
    size: parseInt(serverFile.file_size, 10),
    type: getFileTypeFromName(serverFile.file_name),
    url: serverFile.file_url,
    appointment_file_id: serverFile.appointment_file_id,
    created_ts: serverFile.created_ts,
    isServerFile: true,
  }))
}

/**
 * Determines the file type based on the file name extension
 */
const getFileTypeFromName = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase()

  if (!extension) return 'application/octet-stream'

  const extensionMap: Record<string, string> = {
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
  }

  return extensionMap[extension] || 'application/octet-stream'
}

/**
 * Prepares files for submission in the appointment form
 * Combines both server files and newly uploaded files
 */
export const prepareFilesForSubmission = (files: Array<FileItem>) => {
  if (!files || files.length === 0) return []

  return files.map((file) => {
    if ('appointment_file_id' in file) {
      return {
        appointment_file_id: file.appointment_file_id,
        file_name: file.name,
      }
    }

    return file
  })
}
