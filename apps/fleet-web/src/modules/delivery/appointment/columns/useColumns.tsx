import { useMemo } from 'react'
import { includes } from 'lodash'
import {
  getGridStringOperators,
  GridActionsCellItem,
  useDataGridColumnHelper,
  type GridColDef,
} from '@karoo-ui/core'
import CancelIcon from '@mui/icons-material/Cancel'
import EditIcon from '@mui/icons-material/Edit'
import HistoryIcon from '@mui/icons-material/History'
import MoreVertIcon from '@mui/icons-material/MoreVert'
import { DateTime } from 'luxon'

import { ctIntl } from 'src/util-components/ctIntl'

import type { FetchAppointmentTableColumnListQuery } from '../../api/appointment/useAppointmentTableColumnListQuery'
import type { FetchAppointmentTableListQuery } from '../../api/appointment/useAppointmentTableListQuery'
import AppointmentActionsMenu, {
  type MenuItemType,
} from '../components/AppointmentActionsMenu'

type DataGridRow = FetchAppointmentTableListQuery.Return['appointments'][number]
type ApiColumn = FetchAppointmentTableColumnListQuery.Return[number]

type ColumnTypeHandler = {
  valueGetter?: (row: DataGridRow, field: string) => any
  valueFormatter?: (value: any) => string
  renderCell?: (params: { row: DataGridRow; field: string }) => React.ReactNode
}

type ModalHook<T = unknown> = {
  open: (data?: T) => void
  close: () => void
  data?: T
}

type JobStatus = 'Assign Later' | 'Assigned'

const EDITABLE_STATUSES: Array<JobStatus> = ['Assign Later', 'Assigned']

const TYPE_HANDLERS: Record<string, ColumnTypeHandler> = {
  dateTime: {
    valueFormatter: (value: Date | string | null | undefined) => {
      if (!value) return '-'
      try {
        const date = typeof value === 'string' ? new Date(value) : value
        return DateTime.fromJSDate(date).toFormat('D T')
      } catch {
        return '-'
      }
    },
  },
  time: {
    valueFormatter: (value: Date | string | null | undefined) => {
      if (!value) return '-'
      try {
        const date = typeof value === 'string' ? new Date(value) : value
        return DateTime.fromJSDate(date).toFormat('t')
      } catch {
        return '-'
      }
    },
  },
  duration: {
    valueFormatter: (value: number | null | undefined) => {
      if (value == null) return '-'
      return ctIntl.formatDuration(value * 60)
    },
  },
}

const createBaseValueGetter =
  (field: keyof DataGridRow, typeHandler?: ColumnTypeHandler) => (row: DataGridRow) => {
    const value = row[field]
    return typeHandler?.valueGetter ? typeHandler.valueGetter(row, field) : value
  }

const createRenderCell = (field: string, typeHandler?: ColumnTypeHandler) => {
  if (!typeHandler?.renderCell) return undefined
  return (params: { row: DataGridRow }) =>
    typeHandler.renderCell?.({ row: params.row, field })
}

const isStatusEditable = (status: string): boolean =>
  includes(EDITABLE_STATUSES, status)

export const useColumns = (
  apiColumns: Array<ApiColumn>,
  editModal: ModalHook<number>,
  cancelModal: ModalHook<number>,
  activityModal: ModalHook<number>,
): Array<GridColDef<DataGridRow>> => {
  const columnHelper = useDataGridColumnHelper<DataGridRow>({
    filterMode: 'server',
  })

  return useMemo(() => {
    const buildDateTimeColumn = (
      params: Parameters<typeof columnHelper.dateTime>[0],
    ) => {
      const column = columnHelper.dateTime(params)
      const { getGridDateColumnRangeOperator } = columnHelper.utils

      return {
        ...column,
        filterOperators: [
          getGridDateColumnRangeOperator({
            InputComponentProps: {
              dateRangePickerProps: {
                disableFuture: true,
              },
            },
          }),
        ],
        resizable: true,
      }
    }

    const createDataColumn = (apiColumn: ApiColumn): GridColDef<DataGridRow> => {
      const {
        headerName,
        field,
        type = 'string',
        width,
        format,
        filterable = false,
      } = apiColumn

      const baseColumnProps = {
        field,
        headerName: ctIntl.formatMessage({ id: headerName }),
        width,
        resizable: true,
        filterable,
        type,
      }

      const typeHandler = format ? TYPE_HANDLERS[format] : undefined
      const baseValueGetter = createBaseValueGetter(field, typeHandler)
      const renderCell = createRenderCell(field, typeHandler)

      if (type === 'dateTime') {
        return buildDateTimeColumn({
          ...baseColumnProps,
          valueGetter: (_, row) => baseValueGetter(row),
          valueFormatter: typeHandler?.valueFormatter,
          renderCell,
        })
      }

      if (type === 'string') {
        return columnHelper.string((_, row) => baseValueGetter(row) ?? '-', {
          ...baseColumnProps,
          filterOperators: getGridStringOperators<DataGridRow>().filter(
            (operator) => operator.value === 'contains',
          ),
          valueFormatter: typeHandler?.valueFormatter,
          renderCell,
        })
      }

      return columnHelper.number((_, row) => baseValueGetter(row) ?? 0, {
        ...baseColumnProps,
        valueFormatter: typeHandler?.valueFormatter,
        renderCell,
      })
    }

    const createActionMenuItems = (status: string): Array<MenuItemType<number>> => {
      const baseItems: Array<MenuItemType<number>> = [
        {
          id: 'Activity',
          icon: <HistoryIcon />,
          action: (rowId) => activityModal.open(rowId),
        },
      ]

      if (isStatusEditable(status)) {
        return [
          {
            id: 'Edit',
            icon: <EditIcon />,
            action: (rowId) => editModal.open(rowId),
          },
          ...baseItems,
          {
            id: 'Cancel',
            icon: <CancelIcon />,
            action: (rowId) => cancelModal.open(rowId),
          },
        ]
      }

      return baseItems
    }

    const createActionsColumn = (): GridColDef<DataGridRow> => ({
      field: 'actions',
      type: 'actions',
      width: 80,
      headerName: ctIntl.formatMessage({ id: 'Actions' }),
      sortable: false,
      filterable: false,
      getActions: (params) => {
        const { row } = params
        const actionMenuItems = createActionMenuItems(row.jobStatus)

        return [
          <AppointmentActionsMenu
            rowId={row.appointmentId}
            key="actions"
            menuItems={actionMenuItems}
          >
            <GridActionsCellItem
              icon={<MoreVertIcon />}
              label={ctIntl.formatMessage({ id: 'Actions' })}
              onClick={(event) => event.stopPropagation()}
            />
          </AppointmentActionsMenu>,
        ]
      },
    })

    const dataColumns = apiColumns.map(createDataColumn)
    const actionsColumn = createActionsColumn()

    return [...dataColumns, actionsColumn]
  }, [columnHelper, apiColumns, editModal, cancelModal, activityModal])
}
