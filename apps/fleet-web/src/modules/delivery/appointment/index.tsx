import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  Button,
  CircularProgress,
  DataGrid,
  Stack,
  useCallbackBranded,
  useGridApiRef,
  type GridFilterModel,
  type GridPaginationModel,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/AddRounded'
import { rgba } from 'polished'
import { useHistory, useLocation } from 'react-router-dom'

import PageHeader from 'src/components/_containers/PageHeader'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import useModal from 'src/hooks/use-modal'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'

import useAppointmentTableColumnListQuery, {
  type FetchAppointmentTableColumnListQuery,
} from '../api/appointment/useAppointmentTableColumnListQuery'
import useAppointmentTableListQuery, {
  type FetchAppointmentTableListQuery,
} from '../api/appointment/useAppointmentTableListQuery'
import useGetAppointmentDetailsQuery from '../api/appointment/useGetAppointmentDetailsQuery'
import { useColumns } from './columns/useColumns'
import AppointmentActivityModal from './components/AppointmentActivityModal'
import CancelAppointmentConfirmDialog from './components/CancelAppointmentConfirmDialog'
import AppointmentModal from './components/CreateAppointmentModal'

const AppointmentsDataGrid = () => {
  const apiRef = useGridApiRef()
  const location = useLocation()
  const history = useHistory()
  const [createModalOpen, createModal] = useModal(false)
  const [editModalOpen, editModal] = useModal<number>(false)
  const [cancelDialogOpen, cancelModal] = useModal<number>(false)
  const [activityModalOpen, activityModal] = useModal<number>(false)

  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: PAGE_SIZE_OPTIONS[0],
  })

  const onPaginationModelChange = useCallback(
    (newPaginationModel: GridPaginationModel) => {
      setPaginationModel(newPaginationModel)
    },
    [],
  )

  const [columnVisibilityModel, setColumnVisibilityModel] = useState<
    Record<string, boolean>
  >({})

  const [filters, setFilters] = useState<
    FetchAppointmentTableListQuery.ApiInput['filters']
  >([])

  const appointmentTableColumnListQuery = useAppointmentTableColumnListQuery()

  useEffect(() => {
    if (appointmentTableColumnListQuery.data) {
      const initialVisibilityModel = appointmentTableColumnListQuery.data.reduce(
        (model, column) => ({
          ...model,
          [column.field]: column.visible,
        }),
        {} as Record<string, boolean>,
      )
      setColumnVisibilityModel(initialVisibilityModel)
    }
  }, [appointmentTableColumnListQuery.data])

  const apiColumns =
    useMemo((): FetchAppointmentTableColumnListQuery.ApiOutput['columns'] => {
      if (appointmentTableColumnListQuery.data) {
        return appointmentTableColumnListQuery.data
      }
      return []
    }, [appointmentTableColumnListQuery.data])

  const columns = useColumns(apiColumns, editModal, cancelModal, activityModal)

  const queryInput = useMemo(
    () => ({
      pagination: {
        page: paginationModel.page + 1,
        perPage: paginationModel.pageSize,
      },
      filters: filters ?? [],
    }),
    [paginationModel, filters],
  )

  const appointmentTableListQuery = useAppointmentTableListQuery(queryInput)

  const { tableMeta, rows } = useMemo((): {
    rows: FetchAppointmentTableListQuery.Return['appointments']
    tableMeta: FetchAppointmentTableListQuery.Return['meta']
  } => {
    if (appointmentTableListQuery.data) {
      return {
        tableMeta: appointmentTableListQuery.data.meta,
        rows: appointmentTableListQuery.data.appointments,
      }
    }
    return {
      tableMeta: { currentPage: 1, perPage: 10, totalPages: 0, totalItems: 0 },
      rows: [],
    }
  }, [appointmentTableListQuery.data])

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search)
    const appointmentId = searchParams.get('appointmentId')

    if (appointmentId && !editModalOpen) {
      const appointmentIdNumber = Number(appointmentId)
      if (!isNaN(appointmentIdNumber)) {
        editModal.open(appointmentIdNumber)

        const newSearchParams = new URLSearchParams(location.search)
        newSearchParams.delete('appointmentId')
        const newSearch = newSearchParams.toString()
        const newUrl = `${location.pathname}${newSearch ? `?${newSearch}` : ''}`
        history.replace(newUrl)
      }
    }
  }, [location.search, editModalOpen, editModal, history, location.pathname])

  const handleEditModalClose = useCallback(() => {
    editModal.close()

    const searchParams = new URLSearchParams(location.search)
    if (searchParams.has('appointmentId')) {
      searchParams.delete('appointmentId')
      const newSearch = searchParams.toString()
      const newUrl = `${location.pathname}${newSearch ? `?${newSearch}` : ''}`
      history.replace(newUrl)
    }
  }, [editModal, location.search, location.pathname, history])

  const onFilterModelChange = useCallback(
    async (newGridFilterModel: GridFilterModel) => {
      const filterFieldsWithValues = newGridFilterModel.items.map((item) => {
        const matchingColumn = columns.find((column) => column.field === item.field)
        return {
          field: item.field,
          operator: item.operator as string,
          value: item.value as string,
          type: matchingColumn?.type as 'string' | 'number' | 'dateTime',
          from: undefined,
          to: undefined,
        }
      }, {})

      if (newGridFilterModel.quickFilterValues?.[0]) {
        filterFieldsWithValues.push({
          field: 'search',
          operator: 'contains',
          value: newGridFilterModel.quickFilterValues[0],
          type: 'string',
          from: undefined,
          to: undefined,
        })
      }
      setFilters(filterFieldsWithValues)
    },
    [columns],
  )

  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    event.stopPropagation()
    event.preventDefault()
  }, [])

  const appointmentDetailsQuery = useGetAppointmentDetailsQuery(
    editModalOpen ? editModal.data : undefined,
  )
  const isAppointmentDetailsLoading = appointmentDetailsQuery.isLoading

  useEffect(() => {
    if (editModalOpen && appointmentDetailsQuery.isError) {
      editModal.close()
    }
  }, [editModalOpen, appointmentDetailsQuery.isError, editModal])

  return (
    <>
      <PageWithMainTableContainer>
        <PageHeader>
          <PageHeader.Title>
            {ctIntl.formatMessage({ id: 'Appointments' })}
          </PageHeader.Title>
          <PageHeader.ButtonsContainer>
            <Button
              variant="contained"
              size="small"
              startIcon={<AddIcon />}
              onClick={() => createModal.open()}
            >
              {ctIntl.formatMessage({ id: `Create Appointment` })}
            </Button>
          </PageHeader.ButtonsContainer>
        </PageHeader>
        <UserDataGridWithSavedSettingsOnIDB
          apiRef={apiRef}
          Component={DataGrid}
          dataGridId="delivery-appointments-data-grid"
          getRowId={useCallbackBranded(
            (row: (typeof rows)[number]) => row.appointmentId,
            [],
          )}
          filterMode="server"
          paginationMode="server"
          pagination
          pageSizeOptions={PAGE_SIZE_OPTIONS}
          paginationModel={paginationModel}
          onPaginationModelChange={onPaginationModelChange}
          onFilterModelChange={onFilterModelChange}
          filterDebounceMs={FILTER_DEBOUNCE_MS}
          rows={rows}
          columns={columns}
          rowCount={tableMeta.totalItems}
          loading={
            appointmentTableListQuery.isFetching ||
            appointmentTableColumnListQuery.isFetching
          }
          hideFooterSelectedRowCount
          columnVisibilityModel={columnVisibilityModel}
          onColumnVisibilityModelChange={(newModel) =>
            setColumnVisibilityModel(newModel)
          }
          initialState={{
            pinnedColumns: { right: ['actions'] },
          }}
          slots={{
            toolbar: KarooToolbar,
            noRowsOverlay: () => <DataStatePlaceholder label="No data available" />,
          }}
          slotProps={{
            row: { onContextMenu: handleContextMenu },
            loadingOverlay: {
              variant: 'linear-progress' as any,
              noRowsVariant: 'skeleton',
            },
            noRowsOverlay: {},
            toolbar: KarooToolbar.createProps({
              slots: {
                searchFilter: {
                  show: true,
                  props: { quickSearchDebounceMs: FILTER_DEBOUNCE_MS },
                },
                settingsButton: {
                  show: true,
                  props: {},
                },
                filterButton: { show: true },
              },
              extraContent: {
                left: (
                  <Stack
                    direction="row"
                    gap={1}
                    alignItems="center"
                    flexWrap="wrap"
                  ></Stack>
                ),
                right: (
                  <Stack
                    direction="row"
                    alignItems="center"
                    gap={2}
                  ></Stack>
                ),
              },
            }),
            filterPanel: { logicOperators: [] },
            pagination: {
              slotProps: {
                actions: {
                  previousButton: {
                    disabled: paginationModel.page === 0,
                  },
                  nextButton: {
                    disabled:
                      appointmentTableListQuery.isFetching ||
                      paginationModel.page + 1 >= tableMeta.totalPages,
                  },
                },
              },
            },
          }}
          sx={({ palette }) => ({
            '& .MuiDataGrid-main': { paddingX: 2.5 },
            '& .MuiDataGrid-row': { cursor: 'pointer' },
            '& .MuiDataGrid-row--editing': {
              boxShadow: 'none',
            },
            '& .MuiDataGrid-row--editing .MuiDataGrid-cell': {
              backgroundColor: rgba(
                palette.primary.main,
                palette.action.selectedOpacity,
              ),
            },
            '.MuiCheckbox-root.Mui-checked': {
              color: 'primary.main',
            },
            '.MuiDataGrid-row.Mui-selected': {
              backgroundColor: rgba(
                palette.primary.main,
                palette.action.selectedOpacity,
              ),
              '&:hover': {
                background: rgba(palette.primary.main, palette.action.selectedOpacity),
              },
            },
            '& .MuiDataGrid-row--hover': {
              backgroundColor: rgba(palette.action.hover, palette.action.hoverOpacity),
            },
            '& .MuiDataGrid-columnHeader.header-blank': {
              border: 'none',
              alignItems: 'start',
              padding: 0,

              '& .MuiDataGrid-columnHeaderTitleContainer': {
                justifyContent: 'start',
              },

              '& .MuiDataGrid-columnSeparator': {
                display: 'none',
              },
            },
            '& .MuiDataGrid-cell.cell-blank': {
              border: 'none',
              padding: 0,
              overflow: 'visible',
            },

            '& .MuiDataGrid-row--lastVisible': {
              '.MuiDataGrid-cell.cell-blank': {
                outline: '1px solid white',
              },

              '& .pd-dashed-line': {
                display: 'none',
              },
            },
          })}
        />
      </PageWithMainTableContainer>

      {createModalOpen && (
        <AppointmentModal
          mode="create"
          onClose={createModal.close}
        />
      )}

      {editModalOpen && (
        <>
          {isAppointmentDetailsLoading ? (
            <div
              style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                zIndex: 1300,
              }}
            >
              <CircularProgress />
            </div>
          ) : (
            <AppointmentModal
              mode="edit"
              onClose={handleEditModalClose}
              appointmentId={editModal.data}
            />
          )}
        </>
      )}
      {cancelDialogOpen && (
        <CancelAppointmentConfirmDialog
          open={true}
          onClose={cancelModal.close}
          appointmentId={cancelModal.data}
        />
      )}
      {activityModalOpen && (
        <AppointmentActivityModal
          open={true}
          onClose={activityModal.close}
          appointmentId={activityModal.data}
        />
      )}
    </>
  )
}

export default AppointmentsDataGrid

const PAGE_SIZE_OPTIONS = [10, 25, 50]
const FILTER_DEBOUNCE_MS = 500
