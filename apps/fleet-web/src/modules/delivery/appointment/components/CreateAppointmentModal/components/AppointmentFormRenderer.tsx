import {
  <PERSON><PERSON>ple<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ack,
  styled,
  TextField,
  Typography,
} from '@karoo-ui/core'
import type { UseControlledFormReturn } from '@karoo-ui/core-rhf'
import DeleteIcon from '@mui/icons-material/Delete'
import { DateTime } from 'luxon'
import { Controller, type Path } from 'react-hook-form'
import { v4 as uuidv4 } from 'uuid'

import type { FetchAppointmentFormDetailsQuery } from 'src/modules/delivery/api/appointment/useAppointmentFormDetailsQuery'

import DynamicCustomerSearch from '../../DynamicCustomerSearch'

const DeleteButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  right: theme.spacing(1),
  zIndex: 1,
}))

const ItemCard = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[50],
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(2),
  paddingRight: theme.spacing(6),
  marginTop: theme.spacing(2),
  position: 'relative',
}))

const renderFormTextField = (
  dynamicField: FetchAppointmentFormDetailsQuery.DynamicFormField,
  form: UseControlledFormReturn<FetchAppointmentFormDetailsQuery.DynamicForm>,
  fieldPath: string,
) => {
  const textFieldAttributes =
    dynamicField.attributes as FetchAppointmentFormDetailsQuery.TextFieldAttributes

  const validationRules = dynamicField.validation || {}
  const registerOptions: any = {}

  if (validationRules.required) {
    registerOptions.required = validationRules.message || 'This field is required'
  }

  if (validationRules.minLength) {
    registerOptions.minLength = {
      value: validationRules.minLength,
      message:
        validationRules.message || `Minimum length is ${validationRules.minLength}`,
    }
  }

  if (validationRules.maxLength) {
    registerOptions.maxLength = {
      value: validationRules.maxLength,
      message:
        validationRules.message || `Maximum length is ${validationRules.maxLength}`,
    }
  }

  if (validationRules.pattern) {
    registerOptions.pattern = {
      value: validationRules.pattern,
      message: validationRules.message || 'Invalid format',
    }
  }

  if (validationRules.validateTrigger) {
    registerOptions.validateTrigger = validationRules.validateTrigger
  }

  return (
    <Controller
      name={
        `${fieldPath}.attributes.value` as Path<FetchAppointmentFormDetailsQuery.DynamicForm>
      }
      control={form.control}
      rules={registerOptions}
      render={({ field, fieldState }) => (
        <TextField
          {...field}
          label={dynamicField.label}
          fullWidth
          placeholder={textFieldAttributes.placeholder ?? ''}
          multiline={(textFieldAttributes.rows ?? 1) > 1 ? true : false}
          rows={textFieldAttributes.rows ?? 1}
          error={!!fieldState.error}
          helperText={fieldState.error?.message}
          onChange={(e) => {
            field.onChange(e.target.value)
            form.trigger(
              fieldPath as Path<FetchAppointmentFormDetailsQuery.DynamicForm>,
            )
          }}
        />
      )}
    />
  )
}

const renderFormCustomerSearch = (
  dynamicField: FetchAppointmentFormDetailsQuery.DynamicFormField,
  form: UseControlledFormReturn<FetchAppointmentFormDetailsQuery.DynamicForm>,
  fieldPath: string,
) => (
  <DynamicCustomerSearch
    dynamicField={dynamicField}
    form={form}
    fieldPath={fieldPath}
  />
)

const addNewGroupedField = (
  fields: Array<FetchAppointmentFormDetailsQuery.DynamicFormField>,
  form: UseControlledFormReturn<FetchAppointmentFormDetailsQuery.DynamicForm>,
  fieldPath: string,
) => {
  const currentItems = (form.getValues(
    `${fieldPath}.attributes.groupedFieldsValues` as Path<FetchAppointmentFormDetailsQuery.DynamicForm>,
  ) || []) as Array<FetchAppointmentFormDetailsQuery.GroupedFieldList>
  form.setValue(
    `${fieldPath}.attributes.groupedFieldsValues` as Path<FetchAppointmentFormDetailsQuery.DynamicForm>,
    [...currentItems, { key: uuidv4(), groupedFieldList: fields }],
    {
      shouldValidate: true,
      shouldDirty: true,
    },
  )
  form.trigger()
}

const removeGroupedField = (
  form: UseControlledFormReturn<FetchAppointmentFormDetailsQuery.DynamicForm>,
  fieldPath: string,
  groupedFieldListIndex: number,
) => {
  const currentItems = (form.getValues(
    `${fieldPath}.attributes.groupedFieldsValues` as Path<FetchAppointmentFormDetailsQuery.DynamicForm>,
  ) || []) as Array<FetchAppointmentFormDetailsQuery.GroupedFieldList>

  const updatedItems = currentItems.filter(
    (_, index) => index !== groupedFieldListIndex,
  )

  form.setValue(
    `${fieldPath}.attributes.groupedFieldsValues` as Path<FetchAppointmentFormDetailsQuery.DynamicForm>,
    updatedItems,
    {
      shouldValidate: true,
      shouldDirty: true,
    },
  )
}

const renderFormGroupedFields = (
  dynamicField: FetchAppointmentFormDetailsQuery.DynamicFormField,
  form: UseControlledFormReturn<FetchAppointmentFormDetailsQuery.DynamicForm>,
  fieldPath: string,
) => {
  const groupedFieldsAttributes =
    dynamicField.attributes as FetchAppointmentFormDetailsQuery.GroupedFieldsAttributes

  const validationRules = dynamicField.validation || {}
  const registerOptions: any = {}

  if (validationRules.required) {
    registerOptions.validate = (
      value: Array<FetchAppointmentFormDetailsQuery.GroupedFieldList>,
    ) => {
      if (!value || value.length === 0) {
        return validationRules.message || 'At least one item is required'
      }
      return true
    }
  }

  return (
    <Box>
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
      >
        <Typography variant="subtitle2">{dynamicField.label}</Typography>
        {groupedFieldsAttributes.canAdd && (
          <Button
            variant="text"
            sx={{ textTransform: 'none' }}
            onClick={() =>
              addNewGroupedField(groupedFieldsAttributes.groupedFields, form, fieldPath)
            }
          >
            + Add Item
          </Button>
        )}
      </Stack>
      <Controller
        name={
          `${fieldPath}.attributes.groupedFieldsValues` as Path<FetchAppointmentFormDetailsQuery.DynamicForm>
        }
        control={form.control}
        rules={registerOptions}
        render={({ field: { value }, fieldState }) => (
          <>
            {(value as Array<FetchAppointmentFormDetailsQuery.GroupedFieldList>)?.map(
              (item, groupedFieldListIndex) => (
                <ItemCard key={item.key}>
                  <DeleteButton
                    size="small"
                    onClick={() =>
                      removeGroupedField(form, fieldPath, groupedFieldListIndex)
                    }
                    disabled={
                      (
                        value as Array<FetchAppointmentFormDetailsQuery.GroupedFieldList>
                      ).length === 1
                    }
                  >
                    <DeleteIcon fontSize="small" />
                  </DeleteButton>

                  <Stack spacing={2}>
                    <Box
                      sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}
                    >
                      {item.groupedFieldList
                        .sort((a, b) => (a.orderIndex || 0) - (b.orderIndex || 0))
                        .map((field, groupedFieldIndex) =>
                          renderAppointmentFormDynamicField(
                            field,
                            form,
                            `${fieldPath}.attributes.groupedFieldsValues.${groupedFieldListIndex}.groupedFieldList.${groupedFieldIndex}`,
                          ),
                        )}
                    </Box>
                  </Stack>
                </ItemCard>
              ),
            )}
            {fieldState.error && (
              <Typography
                color="error"
                variant="caption"
                sx={{ mt: 1 }}
              >
                {fieldState.error.message}
              </Typography>
            )}
          </>
        )}
      />
    </Box>
  )
}

const renderFormAutoCompleteField = (
  dynamicField: FetchAppointmentFormDetailsQuery.DynamicFormField,
  form: UseControlledFormReturn<FetchAppointmentFormDetailsQuery.DynamicForm>,
  fieldPath: string,
) => {
  const autoCompleteFieldAttributes =
    dynamicField.attributes as FetchAppointmentFormDetailsQuery.AutoCompleteFieldAttributes

  const validationRules = dynamicField.validation || {}
  const registerOptions: any = {}

  if (validationRules.required) {
    registerOptions.required = validationRules.message || 'This field is required'
  }

  const options = autoCompleteFieldAttributes.options.map((option) => ({
    value: option.value,
    label: option.label,
  }))

  return (
    <Controller
      name={
        `${fieldPath}.attributes.value` as Path<FetchAppointmentFormDetailsQuery.DynamicForm>
      }
      control={form.control}
      rules={registerOptions}
      render={({ field, fieldState }) => (
        <Autocomplete
          options={options}
          getOptionLabel={(option) => option.label}
          value={options.find((option) => option.value === field.value) || null}
          onChange={(_, newValue) => field.onChange(newValue?.value || '')}
          renderInput={(params) => (
            <TextField
              {...params}
              label={dynamicField.label}
              placeholder={autoCompleteFieldAttributes.placeholder || ''}
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              fullWidth
            />
          )}
          fullWidth
        />
      )}
    />
  )
}

const renderFormDatePickerField = (
  dynamicField: FetchAppointmentFormDetailsQuery.DynamicFormField,
  form: UseControlledFormReturn<FetchAppointmentFormDetailsQuery.DynamicForm>,
  fieldPath: string,
) => {
  const datePickerFieldAttributes =
    dynamicField.attributes as FetchAppointmentFormDetailsQuery.DatePickerFieldAttributes

  const validationRules = dynamicField.validation || {}
  const registerOptions: any = {}

  if (validationRules.required) {
    registerOptions.required = validationRules.message || 'This field is required'
  }

  const minDate = datePickerFieldAttributes.minDate
    ? DateTime.fromISO(datePickerFieldAttributes.minDate)
    : undefined
  const maxDate = datePickerFieldAttributes.maxDate
    ? DateTime.fromISO(datePickerFieldAttributes.maxDate)
    : undefined
  const disablePast = datePickerFieldAttributes.disablePast || false
  const disableFuture = datePickerFieldAttributes.disableFuture || false

  return (
    <Controller
      name={
        `${fieldPath}.attributes.value` as Path<FetchAppointmentFormDetailsQuery.DynamicForm>
      }
      control={form.control}
      rules={registerOptions}
      render={({ field, fieldState }) => (
        <DatePicker
          label={dynamicField.label}
          value={field.value ? DateTime.fromISO(field.value as string) : null}
          onChange={(newDate) => field.onChange(newDate ? newDate.toISODate() : null)}
          minDate={minDate}
          maxDate={maxDate}
          disablePast={disablePast}
          disableFuture={disableFuture}
          slotProps={{
            textField: {
              fullWidth: true,
              error: !!fieldState.error,
              helperText: fieldState.error?.message,
              placeholder: datePickerFieldAttributes.placeholder || '',
            },
          }}
        />
      )}
    />
  )
}

export const renderAppointmentFormDynamicField = (
  dynamicField: FetchAppointmentFormDetailsQuery.DynamicFormField,
  form: UseControlledFormReturn<FetchAppointmentFormDetailsQuery.DynamicForm>,
  fieldPath: string,
) => {
  switch (dynamicField.type) {
    case 'text':
      return renderFormTextField(dynamicField, form, fieldPath)
    case 'auto-complete':
      return renderFormAutoCompleteField(dynamicField, form, fieldPath)
    case 'customer-search':
      return renderFormCustomerSearch(dynamicField, form, fieldPath)
    case 'grouped-fields':
      return renderFormGroupedFields(dynamicField, form, fieldPath)
    case 'date-picker':
      return renderFormDatePickerField(dynamicField, form, fieldPath)
    default:
      return <></>
  }
}
