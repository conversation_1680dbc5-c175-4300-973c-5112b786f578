import { useCallback, useEffect, useMemo, useState } from 'react'
import { isEqual } from 'lodash'
import {
  Box,
  Button,
  Dialog,
  IconButton,
  Paper,
  Stack,
  styled,
  Typography,
} from '@karoo-ui/core'
import { useControlledForm } from '@karoo-ui/core-rhf'
import CloseIcon from '@mui/icons-material/Close'
import { format } from 'date-fns'
import { useDropzone } from 'react-dropzone'
import { useWatch } from 'react-hook-form'

import DiscardModal from 'src/components/_modals/Discard'
import useModal from 'src/hooks/use-modal'
import useAppointmentFormDetailsQuery, {
  type FetchAppointmentFormDetailsQuery,
} from 'src/modules/delivery/api/appointment/useAppointmentFormDetailsQuery'
import useCreateAppointmentMutation from 'src/modules/delivery/api/appointment/useCreateAppointmentMutation'
import useGetAppointmentDetailsQuery from 'src/modules/delivery/api/appointment/useGetAppointmentDetailsQuery'
import useGetAvailableAppointmentSlotsQuery from 'src/modules/delivery/api/appointment/useGetAvailableAppointmentSlotsQuery'
import useUpdateAppointmentMutation from 'src/modules/delivery/api/appointment/useUpdateAppointmentMutation'
import { useSpecialEquipmentsOptions } from 'src/modules/delivery/api/special-equipments/useFetchDeliverySpecialEquipments'
import Snackbar from 'src/modules/delivery/utils/snackbar-configuraor'
import { ctIntl } from 'src/util-components/ctIntl'
import { Array_forEach } from 'src/util-functions/performance-critical-utils'

import useDownloadAppointmentFileMutation from '../../../api/appointment/useDownloadAppointmentFileMutation'
import {
  extractKeysAndValuesFromField,
  formatDateTimeWithTimezone,
  populateDynamicFormField,
} from '../../helpers'
import AppointmentCalendar, {
  type EquipmentOption,
  type TimeSlotData,
} from './components/AppointmentCalendar'
import AppointmentFileUploader, {
  type FileItem,
} from './components/AppointmentFileUploader'
import { renderAppointmentFormDynamicField } from './components/AppointmentFormRenderer'
import AppointmentSummary from './components/AppointmentSummary'
import LoadingIndicator from './components/LoadingIndicator'

type Props = {
  onClose: () => void
  mode: 'create' | 'edit'
  appointmentId?: number
}

type FormData = {
  files: Array<FileItem>
}

const ContentContainer = styled('div')({
  display: 'flex',
  height: '100%',
  width: '100%',
})

const MainContent = styled('div')({
  flex: 1,
  borderRight: '1px solid #e0e0e0',
  padding: '24px',
  overflowY: 'auto',
})

const SideContent = styled(Paper)({
  width: '300px',
  padding: '24px',
  display: 'flex',
  flexDirection: 'column',
})

const StepIndicator = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(4),
  '& > *': {
    marginRight: theme.spacing(2),
  },
}))

const StepCircle = styled('div')<{ active?: boolean }>(({ theme, active }) => ({
  width: 24,
  height: 24,
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: active ? theme.palette.primary.main : theme.palette.grey[300],
  color: active ? theme.palette.primary.contrastText : theme.palette.text.primary,
}))

const StepText = styled(Typography)<{ active?: boolean }>(({ theme, active }) => ({
  color: active ? theme.palette.text.primary : theme.palette.text.secondary,
}))

const StepCircleCompleted = styled('div')(({ theme }) => ({
  width: 24,
  height: 24,
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: theme.palette.success.main,
  color: theme.palette.primary.contrastText,
}))

const StepConnector = styled('div')<{ active?: boolean }>(({ theme, active }) => ({
  flex: 1,
  height: 1,
  backgroundColor: active ? theme.palette.primary.main : theme.palette.grey[300],
  margin: '0 8px',
}))

const BlockingOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: theme.palette.grey[300],
  zIndex: 9999,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  backdropFilter: 'blur(2px)',
}))

const APPOINTMENT_DURATION_MINUTES = 45

const AppointmentModal = ({ onClose, mode = 'create', appointmentId }: Props) => {
  const [discardModalOpen, discardModal] = useModal(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlotData | null>(null)
  const [selectedCapabilityId, setSelectedCapabilityId] = useState('')
  const [isInitialized, setIsInitialized] = useState(false)
  const [downloadingFiles, setDownloadingFiles] = useState<Record<number, boolean>>({})
  const [savedFormData, setSavedFormData] = useState<FormData | null>(null)
  const [savedDynamicFormData, setSavedDynamicFormData] =
    useState<FetchAppointmentFormDetailsQuery.DynamicForm | null>(null)
  const [timeSlots, setTimeSlots] = useState<Array<TimeSlotData>>([])
  const [isLoadingTimeSlots, setIsLoadingTimeSlots] = useState(false)

  const { data: specialEquipments, isLoading: isLoadingEquipments } =
    useSpecialEquipmentsOptions()
  const createAppointmentMutation = useCreateAppointmentMutation()
  const updateAppointmentMutation = useUpdateAppointmentMutation()
  const isLoading =
    createAppointmentMutation.isPending || updateAppointmentMutation.isPending

  const appointmentDetailsQuery = useGetAppointmentDetailsQuery(
    mode === 'edit' ? appointmentId : undefined,
  )

  const downloadMutation = useDownloadAppointmentFileMutation()

  const isAppointmentCancelled = useMemo(() => {
    if (mode === 'edit' && appointmentDetailsQuery.data) {
      return (
        appointmentDetailsQuery.data.appointmentCancelReasonId !== undefined &&
        appointmentDetailsQuery.data.appointmentCancelReasonId !== null
      )
    }
    return false
  }, [mode, appointmentDetailsQuery.data])

  const cancellationReason = useMemo(() => {
    if (isAppointmentCancelled && appointmentDetailsQuery.data?.cancelReason) {
      const cancelReason = appointmentDetailsQuery.data.cancelReason as any
      if (typeof cancelReason === 'object' && cancelReason?.description) {
        return cancelReason.description
      }
      if (typeof cancelReason === 'string') {
        return cancelReason
      }
    }
    return null
  }, [isAppointmentCancelled, appointmentDetailsQuery.data?.cancelReason])

  const equipmentOptions: Array<EquipmentOption> = useMemo(() => {
    if (!specialEquipments) return []
    return specialEquipments.data || []
  }, [specialEquipments])

  const dynamicForm = useControlledForm<FetchAppointmentFormDetailsQuery.DynamicForm>({
    defaultValues: {
      formFields: [],
    },
  })

  const appointmentFormDetailsQuery = useAppointmentFormDetailsQuery()

  useEffect(() => {
    if (
      appointmentFormDetailsQuery.data &&
      dynamicForm.getValues('formFields').length === 0
    ) {
      dynamicForm.setValue('formFields', appointmentFormDetailsQuery.data)
    }
  }, [appointmentFormDetailsQuery.data, dynamicForm])

  const formFields = useMemo(
    () => appointmentFormDetailsQuery.data ?? [],
    [appointmentFormDetailsQuery.data],
  )

  const form = useControlledForm<FormData>({
    defaultValues: {
      files: [],
    },
  })

  const files = useWatch({ control: form.control, name: 'files' })

  const populatedFormFields = useMemo(() => {
    if (appointmentDetailsQuery.data && formFields.length > 0) {
      return formFields.map((field) =>
        populateDynamicFormField(field, appointmentDetailsQuery.data.properties),
      )
    }
    return []
  }, [appointmentDetailsQuery.data, formFields])

  useEffect(() => {
    if (
      mode === 'edit' &&
      appointmentDetailsQuery.data &&
      !isInitialized &&
      formFields.length > 0
    ) {
      const appointmentData = appointmentDetailsQuery.data
      if (appointmentData.windowStartTime) {
        const appointmentDate = new Date(appointmentData.windowStartTime)
        setSelectedDate(appointmentDate)
        const hours = appointmentDate.getHours().toString()
        const minutes = appointmentDate.getMinutes().toString()
        const timeString = appointmentData.windowStartTime.toString()
        const timeZone: string =
          timeString.indexOf('+') >= 0
            ? timeString.substring(timeString.indexOf('+'))
            : timeString.substring(timeString.indexOf('-'))
        const timeSlot = `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`
        setSelectedTimeSlot({
          displayValue: timeSlot,
          fullValue: `${timeSlot}:00${timeZone}`,
        })
      }
      if (appointmentData.capabilityId) {
        setSelectedCapabilityId(appointmentData.capabilityId.toString())
      }
      if (populatedFormFields.length > 0) {
        dynamicForm.setValue('formFields', populatedFormFields)
      }
      if (appointmentData.files) {
        const displayFiles = appointmentData.files.map((file) => ({
          name: file.file_name,
          appointmentFileId: Number(file.appointment_file_id),
        }))
        form.reset(
          { files: displayFiles },
          {
            keepDirty: false,
            keepTouched: false,
          },
        )
      }
      setCurrentStep(mode === 'edit' ? 2 : 1)
      setIsInitialized(true)
    }
  }, [
    form,
    mode,
    formFields,
    dynamicForm,
    isInitialized,
    populatedFormFields,
    appointmentDetailsQuery.data,
  ])

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (files) => {
      const currentFiles = form.getValues('files') || []
      const newFiles = [...currentFiles, ...files]
      form.setValue('files', newFiles, { shouldValidate: true, shouldDirty: true })
    },
    accept: {
      'image/*': [],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
        '.docx',
      ],
    },
    multiple: true,
  })

  const isDownloadingAnyFile = useMemo(
    () => Object.keys(downloadingFiles).length > 0,
    [downloadingFiles],
  )

  const getAvailableSlotsInput = useMemo(
    () => ({
      capabilityId: Number(selectedCapabilityId),
      selectedDate: format(selectedDate, 'yyyy-MM-dd'),
      appointmentId: mode === 'edit' && appointmentId ? appointmentId : null,
    }),
    [selectedDate, selectedCapabilityId, mode, appointmentId],
  )

  const getAvailableAppointmentSlotsQuery =
    useGetAvailableAppointmentSlotsQuery(getAvailableSlotsInput)

  useEffect(() => {
    setIsLoadingTimeSlots(getAvailableAppointmentSlotsQuery.isLoading)
    if (getAvailableAppointmentSlotsQuery.data) {
      const availableSlots = getAvailableAppointmentSlotsQuery.data as Array<string>
      const formattedSlots = availableSlots.map((slot) => ({
        displayValue: slot.substring(0, 5),
        fullValue: slot,
      }))
      setTimeSlots(formattedSlots)
    }
  }, [
    mode,
    selectedTimeSlot,
    getAvailableAppointmentSlotsQuery.data,
    getAvailableAppointmentSlotsQuery.isLoading,
  ])

  useEffect(() => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    if (selectedDate < today) {
      setSelectedDate(today)
    }
  }, [selectedDate])

  const trigger = dynamicForm.trigger

  useEffect(() => {
    trigger()
  }, [dynamicForm.formState.isValid, trigger])

  const buildJsonFromDynamicForm = useCallback(
    (dynamicFormData: Array<FetchAppointmentFormDetailsQuery.DynamicFormField>) => {
      let jsonObject: Record<string, any> = {}
      Array_forEach(dynamicFormData, (field) => {
        jsonObject = extractKeysAndValuesFromField(field, jsonObject)
      })
      return jsonObject
    },
    [],
  )

  const closeDialog = () => {
    if (isDownloadingAnyFile) return
    discardModal.close()
    onClose()
  }

  const closeDialogWithDiscardConfirmation = () => {
    if (isDownloadingAnyFile) return

    const dynamicFormValues = dynamicForm.getValues()

    if (
      form.formState.dirtyFields.files ||
      !isEqual(dynamicFormValues.formFields, populatedFormFields)
    ) {
      discardModal.open()
    } else {
      closeDialog()
    }
  }

  const handleNext = () => {
    if (isDownloadingAnyFile) return
    if (savedFormData) {
      form.reset(savedFormData)
    }

    if (savedDynamicFormData) {
      dynamicForm.reset(savedDynamicFormData)
    }
    setCurrentStep(2)
  }

  const handleBack = () => {
    if (isDownloadingAnyFile) return
    const currentFormData = form.getValues()
    const currentDynamicFormData = dynamicForm.getValues()

    setSavedFormData(currentFormData)
    setSavedDynamicFormData(currentDynamicFormData)

    setCurrentStep(1)
  }

  const handleBookAppointment = () => {
    if (isDownloadingAnyFile) return

    dynamicForm.trigger()

    if (!dynamicForm.formState.isValid) {
      Snackbar.error('Please fill in all required fields correctly')
      return
    }

    const formData = form.getValues()
    const dynamicFormData = dynamicForm.getValues().formFields
    const dynamicAppointmentFormData = buildJsonFromDynamicForm(dynamicFormData)

    const formattedTime = formatDateTimeWithTimezone(
      selectedDate,
      selectedTimeSlot?.fullValue,
    )

    const appointmentData = {
      windowStartTime: formattedTime,
      windowEndTime: formattedTime,
      durationInMinutes: APPOINTMENT_DURATION_MINUTES,
      properties: dynamicAppointmentFormData,
      files: formData.files,
      capabilityId: Number(selectedCapabilityId),
    }

    if (mode === 'edit' && appointmentId) {
      const updateData = {
        ...appointmentData,
        appointmentId: appointmentId,
        capabilityId: Number(selectedCapabilityId),
      }

      updateAppointmentMutation.mutate(updateData, {
        onSuccess: () => {
          onClose()
        },
      })
    } else {
      createAppointmentMutation.mutate(appointmentData, {
        onSuccess: () => {
          onClose()
        },
      })
    }
  }

  const handleDownloadFile = (fileId: number, fileName: string) => {
    setDownloadingFiles((prev) => ({ ...prev, [fileId]: true }))

    downloadMutation.mutate(
      {
        appointmentFileId: fileId,
        appointmentId: appointmentId,
        appointmentFileName: fileName,
      },
      {
        onSettled: () => {
          setDownloadingFiles((prev) => {
            const updated = { ...prev }
            delete updated[fileId]
            return updated
          })
        },
      },
    )
  }

  return (
    <>
      <Dialog
        open
        onClose={() =>
          isLoading || isDownloadingAnyFile
            ? null
            : closeDialogWithDiscardConfirmation()
        }
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            height: '80vh',
            maxHeight: '900px',
            position: 'relative',
          },
        }}
      >
        {isDownloadingAnyFile && (
          <BlockingOverlay>
            <LoadingIndicator
              message={ctIntl.formatMessage({ id: 'Downloading file...' })}
            />
          </BlockingOverlay>
        )}
        <ContentContainer>
          <MainContent>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              mb={3}
            >
              <Typography variant="h5">
                {ctIntl.formatMessage({
                  id: mode === 'create' ? 'Add New Appointment' : 'Edit Appointment',
                })}
              </Typography>
              <IconButton
                onClick={closeDialogWithDiscardConfirmation}
                disabled={isDownloadingAnyFile}
              >
                <CloseIcon />
              </IconButton>
            </Stack>
            <StepIndicator>
              {currentStep === 1 ? (
                <StepCircle active>1</StepCircle>
              ) : (
                <StepCircleCompleted>✓</StepCircleCompleted>
              )}
              <StepText active={currentStep === 1}>
                {ctIntl.formatMessage({ id: 'Select time slot' })}
              </StepText>
              <StepConnector active={currentStep === 2} />
              <StepCircle active={currentStep === 2}>2</StepCircle>
              <StepText active={currentStep === 2}>
                {ctIntl.formatMessage({ id: 'Fill Appointment Details' })}
              </StepText>
            </StepIndicator>
            {currentStep === 1 && (
              <AppointmentCalendar
                equipmentOptions={equipmentOptions}
                selectedCapabilityId={selectedCapabilityId}
                setSelectedCapabilityId={setSelectedCapabilityId}
                isLoadingEquipments={isLoadingEquipments}
                selectedDate={selectedDate}
                setSelectedDate={setSelectedDate}
                selectedTimeSlot={selectedTimeSlot}
                setSelectedTimeSlot={setSelectedTimeSlot}
                timeSlots={timeSlots}
                isLoadingTimeSlots={isLoadingTimeSlots}
              />
            )}
            {currentStep === 2 && (
              <Stack spacing={3}>
                {appointmentDetailsQuery.isLoading && mode === 'edit' ? (
                  <LoadingIndicator />
                ) : (
                  <>
                    {isAppointmentCancelled && (
                      <Box
                        sx={{
                          p: 2,
                          borderRadius: 1,
                          border: '1px solid',
                          borderColor: 'error.main',
                        }}
                      >
                        <Typography
                          variant="body2"
                          color="error.dark"
                          sx={{ fontWeight: 'medium', mb: 1 }}
                        >
                          {ctIntl.formatMessage({
                            id: 'This appointment has been cancelled and cannot be modified.',
                          })}
                        </Typography>
                        {cancellationReason && (
                          <Typography
                            variant="body2"
                            color="error.dark"
                            sx={{ fontStyle: 'italic' }}
                          >
                            {`${ctIntl.formatMessage({
                              id: 'Reason:',
                            })} ${cancellationReason}`}
                          </Typography>
                        )}
                      </Box>
                    )}
                    {formFields
                      .sort((a, b) => (a.orderIndex || 0) - (b.orderIndex || 0))
                      .map((item, index) =>
                        renderAppointmentFormDynamicField(
                          item,
                          dynamicForm,
                          `formFields.${index}`,
                        ),
                      )}
                    <AppointmentFileUploader
                      files={files}
                      onFilesChange={(newFiles) =>
                        form.setValue('files', newFiles, {
                          shouldValidate: true,
                          shouldDirty: true,
                        })
                      }
                      onDownloadFile={handleDownloadFile}
                      getRootProps={getRootProps}
                      getInputProps={getInputProps}
                    />
                  </>
                )}
              </Stack>
            )}
          </MainContent>
          <SideContent>
            <AppointmentSummary
              duration={ctIntl.formatMessage({
                id: `${APPOINTMENT_DURATION_MINUTES} minutes`,
              })}
              equipmentLabel={
                selectedCapabilityId
                  ? equipmentOptions.find(
                      (eq) => eq.value.toString() === selectedCapabilityId,
                    )?.label
                  : ''
              }
              timeSlotLabel={
                selectedTimeSlot
                  ? `${selectedTimeSlot.displayValue}, ${format(
                      selectedDate,
                      'EEE, dd MMM yyyy',
                    )}`
                  : ''
              }
              formState={{
                currentStep,
                formStatus: dynamicForm.formState.isValid ? 'valid' : 'incomplete',
              }}
            />
            <Stack
              direction="row"
              spacing={2}
              sx={{ mt: 'auto' }}
            >
              {currentStep === 2 ? (
                <>
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={handleBack}
                    disabled={
                      isLoading || isDownloadingAnyFile || isAppointmentCancelled
                    }
                    fullWidth
                  >
                    {ctIntl.formatMessage({ id: 'Back' })}
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleBookAppointment}
                    disabled={
                      isLoading ||
                      !dynamicForm.formState.isValid ||
                      isDownloadingAnyFile ||
                      isAppointmentCancelled
                    }
                    loading={
                      createAppointmentMutation.isPending ||
                      updateAppointmentMutation.isPending
                    }
                    fullWidth
                  >
                    {ctIntl.formatMessage({
                      id: mode === 'create' ? 'Book Appointment' : 'Update Appointment',
                    })}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={closeDialogWithDiscardConfirmation}
                    disabled={isLoading || isDownloadingAnyFile}
                    fullWidth
                  >
                    {ctIntl.formatMessage({ id: 'Close' })}
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleNext}
                    disabled={!selectedTimeSlot || isDownloadingAnyFile}
                    fullWidth
                  >
                    {ctIntl.formatMessage({ id: 'Next' })}
                  </Button>
                </>
              )}
            </Stack>
          </SideContent>
        </ContentContainer>
      </Dialog>
      {discardModalOpen && (
        <DiscardModal
          open
          onClose={discardModal.close}
          onConfirm={closeDialog}
        />
      )}
    </>
  )
}

export default AppointmentModal
