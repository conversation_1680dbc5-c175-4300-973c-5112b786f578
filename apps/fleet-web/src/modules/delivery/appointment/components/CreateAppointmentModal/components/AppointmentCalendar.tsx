import React, { useCallback } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  IconButton,
  Stack,
  styled,
  TextField,
  Typography,
} from '@karoo-ui/core'
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import { format } from 'date-fns'

import useGetAppointmentSettingsQuery from 'src/modules/delivery/api/appointment/useGetAppointmentSettingsQuery'
import { ctIntl } from 'src/util-components/ctIntl'

const weekDays = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT']

const CalendarGrid = styled('div')({
  display: 'grid',
  gridTemplateColumns: 'repeat(7, 1fr)',
  gap: '8px',
  marginTop: '16px',
})

type CalendarCellProps = {
  selected?: boolean
  disabled?: boolean
}

const CalendarCell = styled('div', {
  shouldForwardProp: (prop) => prop !== 'selected' && prop !== 'disabled',
})<CalendarCellProps>(({ theme, selected, disabled }) => {
  let textColor

  if (selected) {
    textColor = theme.palette.primary.contrastText
  } else if (disabled) {
    textColor = theme.palette.text.disabled
  } else {
    textColor = theme.palette.text.primary
  }

  return {
    padding: '8px',
    textAlign: 'center',
    borderRadius: '4px',
    cursor: disabled ? 'default' : 'pointer',
    backgroundColor: selected ? theme.palette.primary.main : 'transparent',
    color: textColor,
    '&:hover': {
      backgroundColor: !disabled && !selected ? theme.palette.action.hover : undefined,
    },
  }
})

const CalendarContainer = styled('div')({
  display: 'flex',
  gap: '32px',
  alignItems: 'flex-start',
})

const CalendarSection = styled('div')({
  flex: 1,
})

const TimeSlotSection = styled('div')({
  width: '250px',
})

const TimeSlotGrid = styled('div')({
  display: 'grid',
  gridTemplateColumns: 'repeat(2, 1fr)',
  gap: '8px',
})

type TimeSlotProps = {
  selected?: boolean
}

const TimeSlot = styled(Button, {
  shouldForwardProp: (prop) => prop !== 'selected',
})<TimeSlotProps>(({ theme, selected }) => ({
  backgroundColor: selected ? theme.palette.primary.main : undefined,
  color: selected ? theme.palette.primary.contrastText : undefined,
  '&:hover': {
    backgroundColor: selected ? theme.palette.primary.dark : undefined,
  },
}))

const EquipmentContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  marginBottom: theme.spacing(2),
}))

export type TimeSlotData = {
  displayValue: string
  fullValue: string
}

export type EquipmentOption = {
  label: string
  value: string | number
}

type AppointmentCalendarProps = {
  equipmentOptions: Array<EquipmentOption>
  selectedCapabilityId: string
  setSelectedCapabilityId: (id: string) => void
  isLoadingEquipments: boolean
  selectedDate: Date
  setSelectedDate: (date: Date) => void
  selectedTimeSlot: TimeSlotData | null
  setSelectedTimeSlot: (slot: TimeSlotData | null) => void
  timeSlots: Array<TimeSlotData>
  isLoadingTimeSlots: boolean
}

const AppointmentCalendar: React.FC<AppointmentCalendarProps> = ({
  equipmentOptions,
  selectedCapabilityId,
  setSelectedCapabilityId,
  isLoadingEquipments,
  selectedDate,
  setSelectedDate,
  selectedTimeSlot,
  setSelectedTimeSlot,
  timeSlots,
  isLoadingTimeSlots,
}) => {
  const appointmentSettings = useGetAppointmentSettingsQuery()

  const generateCalendarDays = useCallback(
    (limitDaysAhead: number | undefined) => {
      const days = []
      const firstDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1)
      const lastDay = new Date(
        selectedDate.getFullYear(),
        selectedDate.getMonth() + 1,
        0,
      )
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const maxSelectableDate = limitDaysAhead
        ? new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate() + limitDaysAhead,
          )
        : null

      for (let i = 0; i < firstDay.getDay(); i++) {
        days.push(
          <CalendarCell
            key={`empty-${i}`}
            disabled
          />,
        )
      }

      for (let day = 1; day <= lastDay.getDate(); day++) {
        const date = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), day)
        date.setHours(0, 0, 0, 0)
        const isBeforeToday = date < today
        const isAfterLimit = maxSelectableDate !== null && date > maxSelectableDate
        const isOutOfRange = isBeforeToday || isAfterLimit
        const isSelected = date.getTime() === selectedDate.setHours(0, 0, 0, 0)
        days.push(
          <CalendarCell
            key={day}
            selected={isSelected && !isOutOfRange}
            disabled={isOutOfRange}
            onClick={() => {
              if (!isOutOfRange) {
                setSelectedDate(date)
                setSelectedTimeSlot(null)
              }
            }}
          >
            {day}
          </CalendarCell>,
        )
      }

      return days
    },
    [selectedDate, setSelectedDate, setSelectedTimeSlot],
  )

  return (
    <Stack spacing={3}>
      <Box>
        <EquipmentContainer>
          {isLoadingEquipments ? (
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              p={1}
              width="100%"
            >
              <CircularProgress size={24} />
            </Box>
          ) : (
            <Autocomplete
              fullWidth
              size="small"
              options={equipmentOptions}
              getOptionLabel={(option) => option.label}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder={ctIntl.formatMessage({ id: 'Special Equipment' })}
                />
              )}
              value={
                equipmentOptions.find(
                  (option) => option.value.toString() === selectedCapabilityId,
                ) || null
              }
              onChange={(_, newValue) => {
                setSelectedCapabilityId(newValue ? newValue.value.toString() : '')
                setSelectedTimeSlot(null)
              }}
              noOptionsText={ctIntl.formatMessage({ id: 'No equipment matches' })}
              filterOptions={(options, params) => {
                if (!params.inputValue) return options
                return options.filter((option) =>
                  option.label.toLowerCase().includes(params.inputValue.toLowerCase()),
                )
              }}
              disableListWrap
            />
          )}
        </EquipmentContainer>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mt: 1, display: 'block' }}
        >
          {ctIntl.formatMessage({
            id: "The available timeslots are dependent on this field. Select a special equipment if it's appropriate.",
          })}
        </Typography>
      </Box>
      <CalendarContainer>
        <CalendarSection>
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            mb={2}
          >
            <Typography variant="h6">{format(selectedDate, 'MMMM yyyy')}</Typography>
            <Stack
              direction="row"
              spacing={1}
            >
              <IconButton
                onClick={() => {
                  setSelectedDate(
                    new Date(selectedDate.setMonth(selectedDate.getMonth() - 1)),
                  )
                  setSelectedTimeSlot(null)
                }}
              >
                <ChevronLeftIcon />
              </IconButton>
              <IconButton
                onClick={() => {
                  setSelectedDate(
                    new Date(selectedDate.setMonth(selectedDate.getMonth() + 1)),
                  )
                  setSelectedTimeSlot(null)
                }}
              >
                <ChevronRightIcon />
              </IconButton>
            </Stack>
          </Stack>
          {appointmentSettings.isLoading ? (
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              p={1}
              width="100%"
            >
              <CircularProgress size={24} />
            </Box>
          ) : (
            <CalendarGrid>
              {weekDays.map((day) => (
                <Typography
                  key={day}
                  align="center"
                  color="textSecondary"
                >
                  {day}
                </Typography>
              ))}
              {generateCalendarDays(appointmentSettings.data?.allowAdvanceBookingDays)}
            </CalendarGrid>
          )}
        </CalendarSection>
        <TimeSlotSection>
          <Typography
            variant="h6"
            sx={{ mb: 2 }}
          >
            {format(selectedDate, 'EEE, dd MMM yyyy')}
          </Typography>
          {isLoadingTimeSlots ? (
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              p={1}
              width="100%"
            >
              <CircularProgress size={24} />
            </Box>
          ) : (
            <TimeSlotGrid>
              {timeSlots.map((slot) => (
                <TimeSlot
                  key={slot.fullValue}
                  variant="outlined"
                  selected={selectedTimeSlot?.fullValue === slot.fullValue}
                  onClick={() => setSelectedTimeSlot(slot)}
                >
                  {slot.displayValue}
                </TimeSlot>
              ))}
            </TimeSlotGrid>
          )}
        </TimeSlotSection>
      </CalendarContainer>
    </Stack>
  )
}

export default AppointmentCalendar
