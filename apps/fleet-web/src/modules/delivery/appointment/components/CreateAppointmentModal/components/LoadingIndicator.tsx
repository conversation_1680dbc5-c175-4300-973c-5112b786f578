import React from 'react'
import { Box, CircularProgress, Stack, Typography } from '@karoo-ui/core'

type LoadingIndicatorProps = {
  message?: string
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({ message }) => (
  <Box
    display="flex"
    alignItems="center"
    justifyContent="center"
    p={1}
    width="100%"
  >
    <Stack
      alignItems="center"
      spacing={2}
    >
      <CircularProgress />
      {message && <Typography variant="body1">{message}</Typography>}
    </Stack>
  </Box>
)

export default LoadingIndicator
