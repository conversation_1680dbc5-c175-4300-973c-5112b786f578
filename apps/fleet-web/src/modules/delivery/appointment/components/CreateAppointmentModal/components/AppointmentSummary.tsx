import React from 'react'
import { Box, Stack, styled, Typography } from '@karoo-ui/core'

import { ctIntl } from 'src/util-components/ctIntl'

const SummaryItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}))

type AppointmentSummaryProps = {
  duration: string
  formState: {
    formStatus: 'valid' | 'incomplete'
    currentStep: number
  }
  equipmentLabel?: string
  timeSlotLabel?: string
}

const AppointmentSummary: React.FC<AppointmentSummaryProps> = ({
  duration,
  equipmentLabel,
  timeSlotLabel,
  formState,
}) => (
  <Box>
    <Typography
      variant="h6"
      sx={{ mb: 3 }}
    >
      {ctIntl.formatMessage({ id: 'Summary' })}
    </Typography>
    <Box sx={{ flex: 1 }}>
      <SummaryItem>
        <Typography
          variant="subtitle2"
          color="textSecondary"
        >
          {ctIntl.formatMessage({ id: 'Duration' })}
        </Typography>
        <Typography variant="body1">{duration}</Typography>
      </SummaryItem>
      {equipmentLabel && (
        <SummaryItem>
          <Typography
            variant="subtitle2"
            color="textSecondary"
          >
            {ctIntl.formatMessage({ id: 'Special Equipment' })}
          </Typography>
          <Stack
            direction="row"
            alignItems="center"
            spacing={1}
          >
            <Typography variant="body1">{equipmentLabel}</Typography>
          </Stack>
        </SummaryItem>
      )}
      {timeSlotLabel && (
        <SummaryItem>
          <Typography
            variant="subtitle2"
            color="textSecondary"
          >
            {ctIntl.formatMessage({ id: 'Scheduled Arrival' })}
          </Typography>
          <Stack
            direction="row"
            alignItems="center"
            spacing={1}
          >
            <Typography variant="body1">{timeSlotLabel}</Typography>
          </Stack>
        </SummaryItem>
      )}
      {formState.currentStep === 2 && (
        <SummaryItem>
          <Typography
            variant="subtitle2"
            color="textSecondary"
          >
            {ctIntl.formatMessage({ id: 'Form Status' })}
          </Typography>
          <Stack
            direction="row"
            alignItems="center"
            spacing={1}
          >
            {formState.formStatus === 'valid' ? (
              <Typography
                variant="body1"
                color="success.main"
              >
                {ctIntl.formatMessage({ id: 'Valid' })}
              </Typography>
            ) : (
              <Typography
                variant="body1"
                color="error"
              >
                {ctIntl.formatMessage({ id: 'Incomplete' })}
              </Typography>
            )}
          </Stack>
        </SummaryItem>
      )}
    </Box>
  </Box>
)

export default AppointmentSummary
