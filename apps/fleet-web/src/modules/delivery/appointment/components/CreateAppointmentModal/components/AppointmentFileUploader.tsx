import React from 'react'
import { Box, Button, IconButton, Stack, styled, Typography } from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import UploadFileIcon from '@mui/icons-material/UploadFile'
import type { DropzoneInputProps, DropzoneRootProps } from 'react-dropzone'

import { ctIntl } from 'src/util-components/ctIntl'

const DropzoneBox = styled(Box)(({ theme }) => ({
  border: `2px dashed ${theme.palette.divider}`,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(3),
  textAlign: 'center',
  cursor: 'pointer',
  '&:hover': {
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.action.hover,
  },
}))

const SelectedFileItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(1, 2),
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
  marginTop: theme.spacing(1),
}))

const FileLink = styled('span')(({ theme }) => ({
  cursor: 'pointer',
  '&:hover': {
    textDecoration: 'underline',
    color: theme.palette.primary.main,
  },
}))

export type FileItem =
  | {
      name: string
      appointmentFileId: number
    }
  | File

type AppointmentFileUploaderProps = {
  files: Array<FileItem>
  onFilesChange: (files: Array<FileItem>) => void
  onDownloadFile: (fileId: number, name: string) => void
  getRootProps: () => DropzoneRootProps
  getInputProps: () => DropzoneInputProps
}

const AppointmentFileUploader: React.FC<AppointmentFileUploaderProps> = ({
  files,
  onFilesChange,
  onDownloadFile,
  getRootProps,
  getInputProps,
}) => (
  <Box>
    <Typography
      variant="subtitle2"
      gutterBottom
    >
      {ctIntl.formatMessage({ id: 'Upload Files' })}
    </Typography>
    <DropzoneBox {...getRootProps()}>
      <input {...getInputProps()} />
      <Stack
        spacing={1}
        alignItems="center"
      >
        <UploadFileIcon
          fontSize="large"
          color="action"
        />
        <Typography>
          {ctIntl.formatMessage({ id: 'mifleet.imports.file.drop.title' })}
        </Typography>
        <Button
          variant="outlined"
          size="small"
        >
          {ctIntl.formatMessage({ id: 'mifleet.imports.file.drop.browse' })}
        </Button>
        <Typography
          variant="caption"
          color="text.secondary"
        >
          {ctIntl.formatMessage(
            { id: 'global.upload.supportedFormats' },
            { values: { formats: 'Images, PDF, DOC, DOCX' } },
          )}
        </Typography>
      </Stack>
    </DropzoneBox>
    <Stack sx={{ mt: 2 }}>
      {files.map((file, index) => (
        <SelectedFileItem key={`${file.name}-${index}`}>
          <Stack
            direction="row"
            spacing={1}
            alignItems="center"
          >
            <UploadFileIcon
              fontSize="small"
              color="action"
            />
            {'appointmentFileId' in file ? (
              <FileLink
                onClick={() => {
                  if (file.appointmentFileId) {
                    onDownloadFile(file.appointmentFileId, file.name)
                  }
                }}
              >
                <Typography
                  variant="body2"
                  noWrap
                  sx={{ maxWidth: '300px' }}
                >
                  {file.name}
                </Typography>
              </FileLink>
            ) : (
              <Typography
                variant="body2"
                noWrap
                sx={{ maxWidth: '300px' }}
              >
                {file.name}
              </Typography>
            )}
          </Stack>
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation()
              const newFiles = files.filter((_, i) => i !== index)
              onFilesChange(newFiles)
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </SelectedFileItem>
      ))}
    </Stack>
  </Box>
)

export default AppointmentFileUploader
