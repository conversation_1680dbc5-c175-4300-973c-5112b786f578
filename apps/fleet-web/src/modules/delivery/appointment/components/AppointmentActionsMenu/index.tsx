import React, { useState, type ReactElement } from 'react'
import {
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  type PopoverProps,
} from '@karoo-ui/core'
import type { Except } from 'type-fest'

import { ctIntl } from 'src/util-components/ctIntl'

export type MenuItemType<TKey> = {
  id: string
  icon: ReactElement
  action: (rowId: TKey) => void
}

type AppointmentActionsMenuProps<TKey> = {
  rowId: TKey
  children: ReactElement
  menuProps?: Except<PopoverProps, 'open'> & { open?: boolean }
  onClose?: () => void
  menuItems: Array<MenuItemType<TKey>>
}

const AppointmentActionsMenu = <TKey,>({
  rowId,
  children,
  menuProps,
  onClose,
  menuItems,
}: AppointmentActionsMenuProps<TKey>) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl) || menuProps?.open || false

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation()
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
    onClose?.()
  }

  return (
    <>
      {React.cloneElement(children, { onClick: handleClick })}
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        {...(menuProps || {})}
      >
        {menuItems.map((item) => (
          <MenuItem
            key={item.id}
            onClick={() => {
              item.action(rowId)
              handleClose()
            }}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText>{ctIntl.formatMessage({ id: item.id })}</ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </>
  )
}

export default AppointmentActionsMenu
