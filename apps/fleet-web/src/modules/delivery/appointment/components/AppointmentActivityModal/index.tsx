import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Chip,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  ListItem,
  Stack,
  Typography,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { match, P } from 'ts-pattern'

import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { ctIntl } from 'src/util-components/ctIntl'

import useAuditQuery from '../../../api/appointment/useAppointmentAuditQuery'

type AppointmentActivityModalProps = {
  open: boolean
  onClose: () => void
  appointmentId: number | undefined
}

const AppointmentActivityModal = ({
  open,
  onClose,
  appointmentId,
}: AppointmentActivityModalProps) => {
  const appointmentAuditQuery = useAuditQuery(appointmentId, 'appointment')

  if (!open) return null

  return (
    <Dialog
      open={true}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          width="100%"
        >
          <Typography variant="h6">
            {ctIntl.formatMessage({ id: 'Appointment Activity' })}
          </Typography>
          <IconButton
            onClick={onClose}
            color="secondary"
            size="small"
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </Stack>
      </DialogTitle>
      <DialogContent sx={{ height: 400, p: 0 }}>
        {match(appointmentAuditQuery)
          .with({ isLoading: true }, () => (
            <Stack
              alignItems="center"
              justifyContent="center"
              height="100%"
            >
              <CircularProgress />
            </Stack>
          ))
          .with({ isError: true }, () => (
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <DataStatePlaceholder label="Failed to load activity data" />
            </Box>
          ))
          .with({ isSuccess: true, data: [] }, () => (
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <DataStatePlaceholder label="No activity data available" />
            </Box>
          ))
          .with({ isSuccess: true, data: P.array(P._) }, ({ data: activities }) => (
            <>
              {activities.map((activity, index) => (
                <Box key={`${activity.date}-${activity.time}-${index}`}>
                  <ListItem sx={{ py: 2, px: 3, position: 'relative' }}>
                    <Box
                      sx={{
                        position: 'absolute',
                        left: 28,
                        top: 24,
                        width: 10,
                        height: 10,
                        borderRadius: '50%',
                        bgcolor: index === 0 ? 'primary.main' : 'grey.400',
                        transform: 'translateX(-50%)',
                        zIndex: 1,
                      }}
                    />
                    <Stack
                      pl={4}
                      width="100%"
                      spacing={1}
                    >
                      <Stack spacing={0.5}>
                        <Stack
                          direction="row"
                          spacing={1}
                          alignItems="center"
                        >
                          <Typography
                            variant="subtitle1"
                            fontWeight="bold"
                            component="span"
                          >
                            {activity.changedBy}
                          </Typography>
                          <Chip
                            label={activity.action}
                            size="small"
                            color={
                              activity.action === 'cancelled' ? 'error' : 'primary'
                            }
                            variant="outlined"
                          />
                          {activity.cancellationReason && (
                            <Chip
                              label={activity.cancellationReason}
                              size="small"
                              color="error"
                              variant="outlined"
                            />
                          )}
                        </Stack>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                        >
                          {activity.date} • {activity.time}
                        </Typography>
                      </Stack>
                      {activity.changes && activity.changes.length > 0 && (
                        <Accordion
                          sx={{
                            boxShadow: 'none',
                            '&:before': { display: 'none' },
                            bgcolor: 'grey.50',
                          }}
                        >
                          <AccordionSummary
                            expandIcon={<ExpandMoreIcon />}
                            sx={{
                              minHeight: 'auto',
                              '& .MuiAccordionSummary-content': {
                                margin: '8px 0',
                              },
                            }}
                          >
                            <Typography
                              variant="body2"
                              color="primary"
                            >
                              View {activity.changes.length} change
                              {activity.changes.length > 1 ? 's' : ''}
                            </Typography>
                          </AccordionSummary>
                          <AccordionDetails sx={{ pt: 0 }}>
                            <Stack spacing={0.5}>
                              {activity.changes.map((change) => (
                                <Typography
                                  key={change.field}
                                  variant="caption"
                                  color="text.secondary"
                                  sx={{ pl: 1 }}
                                >
                                  •
                                  <Box
                                    component="span"
                                    fontWeight="bold"
                                    sx={{ px: 0.5 }}
                                  >
                                    {change.field}
                                  </Box>
                                  {match(change.action)
                                    .with('updated', () => (
                                      <>
                                        was updated from "
                                        <Box
                                          component="span"
                                          fontWeight="bold"
                                        >
                                          {change.oldValue || 'Empty'}
                                        </Box>
                                        " to "
                                        <Box
                                          component="span"
                                          fontWeight="bold"
                                        >
                                          {change.newValue || 'Empty'}
                                        </Box>
                                        "
                                      </>
                                    ))
                                    .with('added', () => (
                                      <>
                                        was added: "
                                        <Box
                                          component="span"
                                          fontWeight="bold"
                                        >
                                          {change.newValue}
                                        </Box>
                                        "
                                      </>
                                    ))
                                    .with('removed', () => (
                                      <>
                                        was removed: "
                                        <Box
                                          component="span"
                                          fontWeight="bold"
                                        >
                                          {change.oldValue}
                                        </Box>
                                        "
                                      </>
                                    ))
                                    .exhaustive()}
                                </Typography>
                              ))}
                            </Stack>
                          </AccordionDetails>
                        </Accordion>
                      )}
                    </Stack>
                  </ListItem>
                </Box>
              ))}
            </>
          ))
          .otherwise(() => null)}
      </DialogContent>
    </Dialog>
  )
}

export default AppointmentActivityModal
