import { useState } from 'react'
import {
  <PERSON>ton,
  CircularProgress,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
} from '@karoo-ui/core'

import { ctIntl } from 'src/util-components/ctIntl'

import useAppointmentCancellationReasonsQuery from '../../../api/appointment/useAppointmentCancellationReasonsQuery'
import useCancelAppointmentMutation from '../../../api/appointment/useCancelAppointmentMutation'
import useCancelJobHook from '../../../right-panel/Job/hooks/useCancelJob'

type CancelAppointmentConfirmDialogProps = {
  open: boolean
  onClose: () => void
  appointmentId: number | undefined
  jobId?: number
}

const CancelAppointmentConfirmDialog = ({
  open,
  onClose,
  appointmentId,
  jobId,
}: CancelAppointmentConfirmDialogProps) => {
  const [isLoading, setIsLoading] = useState(false)
  const [selectedReasonId, setSelectedReasonId] = useState('')
  const [hasSubmitAttempt, setHasSubmitAttempt] = useState(false)

  const cancelJob = useCancelJobHook({ jobIds: jobId ? [jobId] : [] })
  const cancelAppointmentMutation = useCancelAppointmentMutation()
  const cancellationReasonsQuery = useAppointmentCancellationReasonsQuery()

  const handleReasonChange = (event: SelectChangeEvent<string>) => {
    setSelectedReasonId(event.target.value)
  }

  const handleCancel = async () => {
    if (!appointmentId) return

    setHasSubmitAttempt(true)

    if (!selectedReasonId) {
      return
    }

    setIsLoading(true)
    if (jobId) {
      await cancelJob.mutateAsync()
    }
    await cancelAppointmentMutation.mutateAsync(
      {
        appointmentId,
        cancellationReasonId: selectedReasonId,
      },
      {
        onSuccess: () => {
          setIsLoading(false)
          onClose()
        },
        onError: () => {
          setIsLoading(false)
          onClose()
        },
      },
    )
  }

  if (!open) return null

  const isError = hasSubmitAttempt && !selectedReasonId

  return (
    <Dialog
      open={true}
      onClose={onClose}
      PaperProps={{ sx: { width: 550, height: 250 } }}
    >
      <DialogTitle>{ctIntl.formatMessage({ id: 'Cancel Appointment' })}</DialogTitle>
      <DialogContent>
        <DialogContentText>
          {ctIntl.formatMessage({
            id: 'Are you sure you want to cancel this appointment?',
          })}
        </DialogContentText>

        <FormControl
          required
          fullWidth
          margin="normal"
          error={isError}
        >
          <InputLabel id="cancel-reason-select-label">
            {ctIntl.formatMessage({ id: 'Appointment Cancellation Reason' })}
          </InputLabel>
          <Select
            labelId="cancel-reason-select-label"
            value={selectedReasonId}
            onChange={handleReasonChange}
            label={ctIntl.formatMessage({ id: 'Appointment Cancellation Reason' })}
            disabled={isLoading || cancellationReasonsQuery.isLoading}
          >
            {cancellationReasonsQuery.isLoading ? (
              <MenuItem
                value=""
                disabled
              >
                {ctIntl.formatMessage({ id: 'Loading...' })}
              </MenuItem>
            ) : (
              cancellationReasonsQuery.data?.map((reason) => (
                <MenuItem
                  key={reason.appointment_cancel_reason_id}
                  value={reason.appointment_cancel_reason_id}
                >
                  {reason.cancel_reason_description}
                </MenuItem>
              ))
            )}
          </Select>
          {isError && (
            <FormHelperText>
              {ctIntl.formatMessage({ id: 'Please select a cancellation reason' })}
            </FormHelperText>
          )}
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={onClose}
          disabled={isLoading}
        >
          {ctIntl.formatMessage({ id: 'No' })}
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleCancel}
          disabled={
            isLoading ||
            !appointmentId ||
            cancellationReasonsQuery.isLoading ||
            !cancellationReasonsQuery.data?.length
          }
          startIcon={
            isLoading ? (
              <CircularProgress
                size={16}
                color="inherit"
              />
            ) : null
          }
        >
          {ctIntl.formatMessage({ id: 'Yes' })}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default CancelAppointmentConfirmDialog
