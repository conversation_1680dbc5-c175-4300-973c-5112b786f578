import html2canvas from '@trainiac/html2canvas'
import { DateTime } from 'luxon'
import * as XLSX from 'xlsx'

import { ctIntl } from 'src/util-components/ctIntl'
import { downloadHtmlElementByIdAsPdf } from 'src/util-functions/file-utils'

export type ExcelExportData = {
  graphElementId: string
  eventTableData: Array<Record<string, any>>
  driverGroupTableData?: Array<Record<string, any>>
  driverTableData: Array<Record<string, any>>
  fileName: string
  isSingleGroup: boolean
}

/**
 * Captures a screenshot of an HTML element and converts it to base64
 */
async function captureElementAsBase64(elementId: string): Promise<string> {
  const element = document.getElementById(elementId)
  if (!element) {
    throw new Error(`Element with id "${elementId}" not found`)
  }

  const canvas = await html2canvas(element, {
    backgroundColor: '#ffffff',
    scale: 1,
    useCORS: true,
    allowTaint: true,
  })

  return canvas.toDataURL('image/png')
}

/**
 * Converts base64 image to binary data for Excel
 */
function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binaryString = atob(base64.split(',')[1])
  const bytes = new Uint8Array(binaryString.length)
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.codePointAt(i) || 0
  }
  return bytes.buffer
}

/**
 * Creates a worksheet with an embedded image
 */
function createImageWorksheet(imageBase64: string): XLSX.WorkSheet {
  const ws = XLSX.utils.aoa_to_sheet([
    [ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.graphTab.title' })],
    [''],
    [
      ctIntl.formatMessage({
        id: 'scoreCards.driverScore.export.graphTab.description',
      }),
    ],
  ])

  // Set column widths
  ws['!cols'] = [{ wch: 50 }]

  // Set row heights
  ws['!rows'] = [
    { hpt: 20 },
    { hpt: 300 }, // Space for image
    { hpt: 15 },
  ]

  return ws
}

/**
 * Creates a worksheet from table data
 */
function createTableWorksheet(
  data: Array<Record<string, any>>,
  sheetName: string,
): XLSX.WorkSheet {
  if (data.length === 0) {
    return XLSX.utils.aoa_to_sheet([
      [ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.noData' })],
    ])
  }

  const ws = XLSX.utils.json_to_sheet(data)

  // Auto-size columns
  const range = XLSX.utils.decode_range(ws['!ref'] || 'A1')
  const colWidths: Array<{ wch: number }> = []

  for (let col = range.s.c; col <= range.e.c; col++) {
    let maxWidth = 10
    for (let row = range.s.r; row <= range.e.r; row++) {
      const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
      const cell = ws[cellAddress]
      if (cell && cell.v) {
        const cellLength = cell.v.toString().length
        maxWidth = Math.max(maxWidth, cellLength)
      }
    }
    colWidths.push({ wch: Math.min(maxWidth + 2, 50) })
  }

  ws['!cols'] = colWidths

  return ws
}

/**
 * Main function to export scorecard data to Excel
 */
export async function exportScorecardToExcel({
  graphElementId,
  eventTableData,
  driverGroupTableData,
  driverTableData,
  fileName,
  isSingleGroup,
}: ExcelExportData): Promise<void> {
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new()

    // Tab 1: Graph (screenshot)
    try {
      const imageBase64 = await captureElementAsBase64(graphElementId)
      const graphWorksheet = createImageWorksheet(imageBase64)
      XLSX.utils.book_append_sheet(
        workbook,
        graphWorksheet,
        ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.tabs.graph' }),
      )
    } catch (error) {
      console.error('Failed to capture graph screenshot:', error)
      // Create empty graph sheet if screenshot fails
      const emptyGraphWorksheet = XLSX.utils.aoa_to_sheet([
        [ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.graphTab.error' })],
      ])
      XLSX.utils.book_append_sheet(
        workbook,
        emptyGraphWorksheet,
        ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.tabs.graph' }),
      )
    }

    // Tab 2: Event table
    const eventWorksheet = createTableWorksheet(
      eventTableData,
      ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.tabs.event' }),
    )
    XLSX.utils.book_append_sheet(
      workbook,
      eventWorksheet,
      ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.tabs.event' }),
    )

    // Tab 3: Driver group table (only for multi-group)
    if (!isSingleGroup && driverGroupTableData && driverGroupTableData.length > 0) {
      const driverGroupWorksheet = createTableWorksheet(
        driverGroupTableData,
        ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.tabs.driverGroup' }),
      )
      XLSX.utils.book_append_sheet(
        workbook,
        driverGroupWorksheet,
        ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.tabs.driverGroup' }),
      )
    }

    // Tab 4: Driver table
    const driverWorksheet = createTableWorksheet(
      driverTableData,
      ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.tabs.driver' }),
    )
    XLSX.utils.book_append_sheet(
      workbook,
      driverWorksheet,
      ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.tabs.driver' }),
    )

    // Generate filename with timestamp
    const timestamp = DateTime.now().toFormat('yyyy-MM-dd_HHmm')
    const finalFileName = `${fileName}_${timestamp}.xlsx`

    // Write and download the file
    XLSX.writeFile(workbook, finalFileName)
  } catch (error) {
    console.error('Failed to export Excel file:', error)
    throw new Error(
      ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.error.general' }),
    )
  }
}
