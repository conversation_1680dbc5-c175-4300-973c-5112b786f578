import type { DriverId } from 'api/types'
import { ctIntl } from 'src/util-components/ctIntl'

import type { FetchPeriodGroupScoresData } from '../api/queries'
import type { RiskEventRow } from '../components/types'
import { eventCategoryOptions } from '../components/utils'
import type { DriverPerformanceRow } from '../Overview/DriverPerformance/types'

/**
 * Transform event data for Excel export
 */
export function transformEventDataForExcel(
  riskEventData: Array<RiskEventRow>,
  isSingleGroup: boolean,
): Array<Record<string, any>> {
  const result: Array<Record<string, any>> = []

  for (const row of riskEventData) {
    if (row.type === 'group') {
      // For group rows (categories), add the category information
      const categoryOption = eventCategoryOptions.find(
        (option) => row.eventName === ctIntl.formatMessage({ id: option.label }),
      )

      const baseRow = {
        [ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.eventTable.eventCategory',
        })]: categoryOption
          ? ctIntl.formatMessage({ id: categoryOption.label })
          : row.eventName,
        [ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.eventTable.event',
        })]: '',
        [ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.eventTable.count',
        })]: row.count.metric.display || '',
        [ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.eventTable.rate' })]:
          row.rate.metric.display || '',
        [ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.eventTable.scoreImpact',
        })]: row.scoreImpact || 0,
      }

      if (!isSingleGroup) {
        baseRow[ctIntl.formatMessage({ id: 'Group' })] = row.eventName
      }

      result.push(baseRow)
    } else if (row.type === 'event') {
      // For event rows, add the specific event information
      const categoryOption = eventCategoryOptions.find(
        (option) => option.value === row.category,
      )

      const baseRow = {
        [ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.eventTable.eventCategory',
        })]: categoryOption
          ? ctIntl.formatMessage({ id: categoryOption.label })
          : row.category,
        [ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.eventTable.event',
        })]: row.eventName,
        [ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.eventTable.count',
        })]: row.count.display || '',
        [ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.eventTable.rate' })]:
          row.rate.display || '',
        [ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.eventTable.scoreImpact',
        })]: row.scoreImpact || 0,
      }

      if (!isSingleGroup) {
        // For multi-group, we need to extract group name from the path
        const groupName = row.path[0]?.replace('group-', '') || ''
        baseRow[ctIntl.formatMessage({ id: 'Group' })] = groupName
      }

      result.push(baseRow)
    }
  }

  return result
}

/**
 * Transform driver group data for Excel export (only for multi-group)
 */
export function transformDriverGroupDataForExcel(
  driverPerformanceData: Array<DriverPerformanceRow>,
): Array<Record<string, any>> {
  const result: Array<Record<string, any>> = []

  for (const row of driverPerformanceData) {
    if (row.type === 'group') {
      const changeText =
        row.scoreChange.direction === 'unchanged'
          ? ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.noChange' })
          : `${row.scoreChange.direction === 'increase' ? '+' : '-'}${row.scoreChange.value}`

      result.push({
        [ctIntl.formatMessage({ id: 'Group' })]: row.driverName,
        [ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.driverGroupTable.peopleCount',
        })]: row.peopleCount,
        [ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.driverGroupTable.changeSinceLastPeriod',
        })]: changeText,
        [ctIntl.formatMessage({ id: 'Score' })]: row.score,
      })
    }
  }

  return result
}

/**
 * Transform driver data for Excel export
 */
export function transformDriverDataForExcel(
  driverPerformanceData: Array<DriverPerformanceRow>,
  rankingData: FetchPeriodGroupScoresData['drivers'],
  driverNames: Map<DriverId, string>,
  isSingleGroup: boolean,
): Array<Record<string, any>> {
  const result: Array<Record<string, any>> = []

  if (isSingleGroup) {
    // For single group, use ranking data which has all drivers
    for (const driver of rankingData) {
      const driverName = driverNames.get(driver.id) || `Driver ${driver.id}`
      const changeValue =
        driver.prevScore && driver.score ? driver.score - driver.prevScore : 0
      const changeText =
        changeValue === 0
          ? ctIntl.formatMessage({ id: 'scoreCards.driverScore.export.noChange' })
          : `${changeValue > 0 ? '+' : ''}${changeValue}`

      result.push({
        [ctIntl.formatMessage({ id: 'Driver' })]: driverName,
        [ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.driverTable.changeSinceLastPeriod',
        })]: changeText,
        [ctIntl.formatMessage({ id: 'Score' })]: driver.score || 0,
      })
    }
  } else {
    // For multi-group, use driver performance data which is grouped
    let currentGroup = ''

    for (const row of driverPerformanceData) {
      if (row.type === 'group') {
        currentGroup = row.driverName
      } else if (row.type === 'driver') {
        let changeText = ctIntl.formatMessage({
          id: 'scoreCards.driverScore.export.noChange',
        })
        if (row.scoreChange) {
          if (row.scoreChange.direction === 'unchanged') {
            changeText = ctIntl.formatMessage({
              id: 'scoreCards.driverScore.export.noChange',
            })
          } else {
            const sign = row.scoreChange.direction === 'increase' ? '+' : '-'
            changeText = `${sign}${row.scoreChange.value}`
          }
        }

        result.push({
          [ctIntl.formatMessage({ id: 'Group' })]: currentGroup,
          [ctIntl.formatMessage({ id: 'Driver' })]: row.driverName,
          [ctIntl.formatMessage({
            id: 'scoreCards.driverScore.export.driverTable.changeSinceLastPeriod',
          })]: changeText,
          [ctIntl.formatMessage({ id: 'Score' })]: row.score || 0,
        })
      }
    }
  }

  return result
}

/**
 * Main function to transform all data for Excel export
 */
export function transformScorecardDataForExcel({
  riskEventData,
  driverPerformanceData,
  rankingData,
  driverNames,
  isSingleGroup,
}: {
  riskEventData: Array<RiskEventRow>
  driverPerformanceData: Array<DriverPerformanceRow>
  rankingData: FetchPeriodGroupScoresData['drivers']
  driverNames: Map<DriverId, string>
  isSingleGroup: boolean
}) {
  const eventTableData = transformEventDataForExcel(riskEventData, isSingleGroup)

  const driverGroupTableData = isSingleGroup
    ? undefined
    : transformDriverGroupDataForExcel(driverPerformanceData)

  const driverTableData = transformDriverDataForExcel(
    driverPerformanceData,
    rankingData,
    driverNames,
    isSingleGroup,
  )

  return {
    eventTableData,
    driverGroupTableData,
    driverTableData,
  }
}
