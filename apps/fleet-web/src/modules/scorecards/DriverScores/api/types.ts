import { z } from 'zod/v4'

import type {
  DriverGroupId,
  DriverId,
  DriverScoresComparisonGroupId,
  VehicleGroupId,
} from 'api/types'

import {
  scoreCardWeightageAiCameraApiKeys,
  scoreCardWeightageAiCollsionApiKeys,
  scoreCardWeightageDriverApiKeys,
  scoreCardWeightageHarshApiKeys,
  scoreCardWeightageSpeedApiKeys,
} from '../../constants'

export declare namespace SharedDriverScores {
  type GroupInput = {
    id: DriverScoresComparisonGroupId
    vehicleTypeId: string | null
    driverGroupIds: ReadonlySet<DriverGroupId> | 'all'
    vehicleGroupIds: ReadonlySet<VehicleGroupId> | 'all'
  }

  type ApiFnInput = {
    start_date: Date
    end_date: Date
    groups: ReadonlyArray<GroupInput>
  }

  type ApiGroupInput = {
    id: DriverScoresComparisonGroupId
    vehicle_type_id: null | string
    driver_group_ids: Array<DriverGroupId> | 'all'
    vehicle_group_ids: Array<VehicleGroupId> | 'all'
  }

  type ApiInput = {
    start_date: Date
    end_date: Date
    groups: Array<ApiGroupInput>
  }
}

// the schema for the group_data part of the response
export const categoryCommonSchema = z.object({
  total_demerits: z.number().nullable(),
})

export const categorySpeedingWithoutEventsSchema = categoryCommonSchema.extend({
  total_duration: z.number().nullable(),
  previous_total_duration: z.number().nullable(),
  percentage: z.number().nullable(),
  previous_percentage: z.number().nullable(),
})

export const categoryNonSpeedingWithoutEventsSchema = categoryCommonSchema.extend({
  total_events: z.number().nullable(),
  previous_total_events: z.number().nullable(),
})

export const eventSpeedingSchema = categorySpeedingWithoutEventsSchema

export const eventNonSpeedingSchema = categoryNonSpeedingWithoutEventsSchema

export const categorySpeedingSchema = categorySpeedingWithoutEventsSchema.extend({
  type: z.literal('speeding'),
  score_breakdown: z.array(
    eventSpeedingSchema.extend({
      type: z.enum(scoreCardWeightageSpeedApiKeys.map((s) => s.toUpperCase())),
    }),
  ),
})

export const categoryHarshDrivingSchema = categoryNonSpeedingWithoutEventsSchema.extend(
  {
    type: z.literal('harsh_driving'),
    score_breakdown: z.array(
      eventNonSpeedingSchema.extend({
        type: z.enum(scoreCardWeightageHarshApiKeys.map((s) => s.toUpperCase())),
      }),
    ),
  },
)

export const categoryAICameraSchema = categoryNonSpeedingWithoutEventsSchema.extend({
  type: z.literal('ai_camera_events'),
  score_breakdown: z.array(
    eventNonSpeedingSchema.extend({
      type: z.enum(
        [
          ...scoreCardWeightageDriverApiKeys,
          ...scoreCardWeightageAiCameraApiKeys,
          ...scoreCardWeightageAiCollsionApiKeys,
        ].map((s) => s.toUpperCase()),
      ),
    }),
  ),
})

export const periodGroupScoreCategorySchema = z.discriminatedUnion('type', [
  categorySpeedingSchema,
  categoryHarshDrivingSchema,
  categoryAICameraSchema,
])

export type EventCategory = z.infer<typeof periodGroupScoreCategorySchema>['type']

export type PeriodGroupScoreCategory = z.infer<typeof periodGroupScoreCategorySchema>

export type PeriodScoreWithoutId = {
  total_distance: number
  total_duration: number
  score_sum: number
  score_days: number
  avg_score: number
  previous_avg_score: number | null
  scores: Array<number | null>
}

export type GroupDriverDatumWithoutId = {
  avg_score: number | null
  avg_previous_score: number | null

  total_distance: number | null
  previous_total_distance: number | null
  total_duration: number | null
  previous_total_duration: number | null

  score_breakdowns: Array<PeriodGroupScoreCategory>
}

export declare namespace FetchPeriodGroupScores {
  type ApiInput = SharedDriverScores.ApiInput

  type DriverApiFnInput = {
    start_date: Date
    end_date: Date
    driver_id: DriverId
  }

  type ApiOutput = {
    period_scores: Array<
      // it is temp_id from FE
      PeriodScoreWithoutId & {
        frontend_group_id: string
      }
    >
    group_data: Array<
      GroupDriverDatumWithoutId & {
        frontend_group_id: string
        drivers: Array<{
          client_driver_id: string
          avg_score: number | null
          previous_avg_score: number | null
          total_distance: number | null
          position_rank: number | null
          total_rank: number | null
          total_score: number | null
          date_trips_count: number | null
          total_events: number | null
        }>
      }
    >
    safety_score_target: { enabled: boolean; value: number }
  }
}

export declare namespace FetchDriverScore {
  type ApiFnInput = {
    startDate: Date
    endDate: Date
    driverId: string
  }
  type ApiInput = {
    start_date: Date
    end_date: Date
    client_driver_id: string
    vehicle_type_id: null | string
    vehicle_group_ids: Array<string> | 'all'
  }

  type ApiOutput = {
    period_scores: Array<
      // it is temp_id from FE
      PeriodScoreWithoutId & {
        client_driver_id: string
      }
    >
    ranking: Array<
      GroupDriverDatumWithoutId & {
        client_driver_id: string
        frontend_groups_id: Array<string>

        vehicle_id: string
        vehicle_type_id: number | null
        total_rank: number
        total_score: number
      }
    >
    safety_score_target: { enabled: boolean; value: number }
  }
}

export declare namespace FetchDefaultDriverScores {
  type ApiOutput = Record<string, number | null>
}
