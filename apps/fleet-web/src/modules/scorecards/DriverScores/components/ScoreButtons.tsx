import { useState } from 'react'
import {
  But<PERSON>,
  colors,
  Dialog,
  Divider,
  <PERSON>u,
  <PERSON>uItem,
  <PERSON>ack,
  Typography,
} from '@karoo-ui/core'
import ExportIcon from '@mui/icons-material/Download'
import InfoOutlineIcon from '@mui/icons-material/InfoOutline'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import SettingsIcon from '@mui/icons-material/Settings'
import { FormattedMessage } from 'react-intl'
import { useHistory } from 'react-router'

import { SETTINGS_PATH_PREFIX } from 'src/modules/app/components/routes/scorecards'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'

import { ColorScore } from '../../utils'

export type ExportType = 'pdf' | 'excel'

export const ScoreButtons = ({
  onExport,
}: {
  onExport: (type: ExportType) => void
}) => {
  const history = useHistory()
  const [showScoreCalculation, setShowScoreCalculation] = useState(false)
  const [exportMenuAnchorEl, setExportMenuAnchorEl] = useState<null | HTMLElement>(null)
  const isExportMenuOpen = Boolean(exportMenuAnchorEl)

  const handleExportMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setExportMenuAnchorEl(event.currentTarget)
  }

  const handleExportMenuClose = () => {
    setExportMenuAnchorEl(null)
  }

  const handleExportOptionClick = (type: ExportType) => {
    onExport(type)
    handleExportMenuClose()
  }

  return (
    <Stack
      spacing={1}
      direction="row"
    >
      <Button
        size="small"
        variant="outlined"
        color="primary"
        startIcon={<InfoOutlineIcon />}
        data-testid="DriverScores-ScoreCalculation"
        onClick={() => {
          setShowScoreCalculation(true)
        }}
      >
        {ctIntl.formatMessage({ id: 'scoreCards.driverScore.scoreCalculation.title' })}
      </Button>
      <Button
        size="small"
        variant="contained"
        color="primary"
        startIcon={<ExportIcon />}
        endIcon={<KeyboardArrowDownIcon />}
        data-testid="DriverScores-Export"
        onClick={handleExportMenuClick}
      >
        {ctIntl.formatMessage({ id: 'Export' })}
      </Button>
      <Menu
        anchorEl={exportMenuAnchorEl}
        open={isExportMenuOpen}
        onClose={handleExportMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={() => handleExportOptionClick('pdf')}>
          {ctIntl.formatMessage({ id: 'Export as PDF' })}
        </MenuItem>
        <MenuItem onClick={() => handleExportOptionClick('excel')}>
          {ctIntl.formatMessage({ id: 'Export as Excel' })}
        </MenuItem>
      </Menu>
      {showScoreCalculation && (
        <Dialog
          open={true}
          onClose={() => setShowScoreCalculation(false)}
        >
          <Stack
            sx={{
              borderRadius: 1,
              padding: 2,
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: 1,
              width: '500px',
            }}
          >
            <Stack
              spacing={2}
              justifyContent="space-between"
            >
              <IntlTypography
                variant="subtitle2"
                msgProps={{ id: 'scoreCards.driverScore.scoreCalculation.title' }}
              />
              <FormattedMessage
                id="scoreCards.driverScore.scoreCalculation.description"
                values={{
                  br: <br />,
                }}
              />
              <Button
                variant="contained"
                startIcon={<SettingsIcon />}
                onClick={() => {
                  history.push(`${SETTINGS_PATH_PREFIX}?tab=configuration`)
                }}
              >
                CONFIGURE WEIGHTAGE
              </Button>
            </Stack>
            <Stack
              direction="column"
              spacing={1}
              sx={{
                backgroundColor: 'grey.50',
                p: 2,
                borderRadius: 2,
                justifyContent: 'space-evenly',
              }}
            >
              <Stack
                direction="row"
                justifyContent="space-between"
              >
                <IntlTypography
                  msgProps={{ id: 'scoreCards.driverScore.scoreCalculation.initial' }}
                />
                <Typography color="success.main">100</Typography>
              </Stack>
              <Stack
                direction="row"
                justifyContent="space-between"
              >
                <IntlTypography
                  msgProps={{ id: 'scoreCards.settings.weightage.speeding.title' }}
                />
                <Typography color="error.main">-12</Typography>
              </Stack>
              <Stack
                direction="row"
                justifyContent="space-between"
              >
                <IntlTypography
                  msgProps={{ id: 'scoreCards.settings.weightage.harsh.title' }}
                />
                <Typography color="error.main">-12</Typography>
              </Stack>
              <Stack
                direction="row"
                justifyContent="space-between"
              >
                <IntlTypography
                  msgProps={{
                    id: 'scoreCards.settings.weightage.aiCamera.title',
                  }}
                />
                <Typography color="error.main">-1</Typography>
              </Stack>
              <Divider />
              <Stack
                direction="row"
                justifyContent="space-between"
                sx={{ mt: 2 }}
              >
                <Typography color="text.primary">Final Score</Typography>
                <ColorScore
                  sx={{
                    color: 'success.main',
                    backgroundColor: colors['green'][50],
                  }}
                >
                  75
                </ColorScore>
              </Stack>
            </Stack>
          </Stack>
        </Dialog>
      )}
    </Stack>
  )
}
