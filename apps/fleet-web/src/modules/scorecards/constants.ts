import { z } from 'zod/v4'
import type { ParsePayload } from 'zod/v4/core'

import {
  scoreCardRefreshPeriodIdSchema,
  scoreCardScoreCountRuleIdSchema,
  scoreCardWeightageIdSchema,
} from 'api/types'

export const MAX_SCORE = 100

export const weightageSchema = z.object({
  enabled: z.boolean(),
  weightageId: scoreCardWeightageIdSchema,
  custom: z.number().min(0).max(MAX_SCORE),
})

export type WeightageValue = z.infer<typeof weightageSchema>

// Helper function to get weightage severity value for comparison
export const getWeightageSeverityValue = (
  weightageValue: WeightageValue,
  weightageLevels: Array<{
    weightageLevelId: string
    threshold: number | null
    value: string
  }>,
): number => {
  if (!weightageValue.enabled) return 0

  const level = weightageLevels.find(
    (level) => level.weightageLevelId === weightageValue.weightageId,
  )

  if (level?.value.toLowerCase() === 'custom') {
    return weightageValue.custom
  }

  return level?.threshold ?? 0
}

const checkWeightageSpeedLevelConflict = ({
  ctx,
  speedingEventKeys,
  weightageLevels,
}: {
  ctx: ParsePayload<WeightageConfigType>
  speedingEventKeys: Array<keyof WeightageConfigType['form']>
  weightageLevels: Array<{
    weightageLevelId: string
    threshold: number | null
    value: string
  }>
}) => {
  const data = ctx.value

  // Get severity values for enabled speeding events
  const enabledSpeedingEvents = speedingEventKeys
    .map((eventKey) => ({
      key: eventKey,
      value: data.form[eventKey],
      severityValue: getWeightageSeverityValue(data.form[eventKey], weightageLevels),
    }))
    .filter((event) => event.value.enabled)

  // Check if we have at least 2 enabled events to compare
  if (enabledSpeedingEvents.length < 2) {
    return
  }

  // Check progression: higher speed should have equal or higher severity than ALL previous events
  for (let i = 1; i < enabledSpeedingEvents.length; i++) {
    const currentEvent = enabledSpeedingEvents[i]

    // Check against all previous events, not just the immediate previous one
    for (let j = 0; j < i; j++) {
      const previousEvent = enabledSpeedingEvents[j]

      if (currentEvent.severityValue < previousEvent.severityValue) {
        ctx.issues.push({
          code: 'custom',
          message: 'scoreCards.settings.weightage.speeding.error.weightageLevelLower',
          path: ['form', currentEvent.key],
          input: data.form[currentEvent.key],
        })
        // Break to avoid duplicate errors for the same field
        break
      }
    }
  }
}

const baseWeightageConfigSchema = z.object({
  form: z.object({
    // wrap the whole form within field `form` to allow to set the form with default values
    speeding_over: weightageSchema,
    speeding_10: weightageSchema,
    speeding_20: weightageSchema,
    speeding_30: weightageSchema,
    speeding_limit: weightageSchema,
    speeding_limit100: weightageSchema,
    speeding_limit140: weightageSchema,
    speeding_limit160: weightageSchema,
    idling: weightageSchema,
    harsh_turning: weightageSchema,
    harsh_braking: weightageSchema,
    harsh_acceleration: weightageSchema,
    harsh_rpm: weightageSchema,
    aiCamera_smoking: weightageSchema,
    aiCamera_cellPhone: weightageSchema,
    aiCamera_distracted: weightageSchema,
    aiCamera_fatigued: weightageSchema,
    aiCamera_cameraCovered: weightageSchema,
    aiCamera_seatbelt: weightageSchema,
    aiCamera_forwardCollision: weightageSchema,
    aiCamera_followingDistance: weightageSchema,
    aiCamera_collision: weightageSchema,
  }),
})

export const scoreCardWeightageSpeedApiKeys = [
  'over_road_speed',
  'over_road_speed_10km',
  'over_road_speed_20km',
  'over_road_speed_30km',
  'over_vehicle_speed_limit',
  'over_vehicle_speed_limit_100km',
  'over_vehicle_speed_limit_140km',
  'over_vehicle_speed_limit_160km',
] as const

export const scoreCardWeightageHarshApiKeys = [
  'harsh_turning',
  'harsh_braking',
  'harsh_acceleration',
  'harsh_rpm',
] as const

export const scoreCardWeightageDriverApiKeys = ['driver_idling'] as const

export const scoreCardWeightageAiCameraApiKeys = [
  'cam_covered',
  'driver_using_cell_phone',
  'driver_smoking',
  'driver_distracted',
  'driver_fatigued',
  'driver_not_wearing_seatbelt',
] as const

export const scoreCardWeightageAiCollsionApiKeys = [
  'forward_collision_warning_triggered',
  'following_distance',
  'collision',
] as const

export const scorecardRawWeightageApiKeys = [
  ...scoreCardWeightageSpeedApiKeys,
  ...scoreCardWeightageHarshApiKeys,
  ...scoreCardWeightageDriverApiKeys,
  ...scoreCardWeightageAiCameraApiKeys,
  ...scoreCardWeightageAiCollsionApiKeys,
]

export const weightageApiKeyToFormPathMap = [
  {
    formPath: 'speeding_over',
    apiKey: 'over_road_speed',
    label: 'scoreCards.settings.weightage.speeding.over.title',
  },
  {
    formPath: 'speeding_10',
    apiKey: 'over_road_speed_10km',
    label: 'scoreCards.settings.weightage.speeding.over10.title',
  },
  {
    formPath: 'speeding_20',
    apiKey: 'over_road_speed_20km',
    label: 'scoreCards.settings.weightage.speeding.over20.title',
  },
  {
    formPath: 'speeding_30',
    apiKey: 'over_road_speed_30km',
    label: 'scoreCards.settings.weightage.speeding.over30.title',
  },
  {
    formPath: 'speeding_limit',
    apiKey: 'over_vehicle_speed_limit',
    label: 'scoreCards.settings.weightage.speeding.overLimit.title',
  },
  {
    formPath: 'speeding_limit100',
    apiKey: 'over_vehicle_speed_limit_100km',
    label: 'scoreCards.settings.weightage.speeding.overLimit100.title',
  },
  {
    formPath: 'speeding_limit140',
    apiKey: 'over_vehicle_speed_limit_140km',
    label: 'scoreCards.settings.weightage.speeding.overLimit140.title',
  },
  {
    formPath: 'speeding_limit160',
    apiKey: 'over_vehicle_speed_limit_160km',
    label: 'scoreCards.settings.weightage.speeding.overLimit160.title',
  },
  {
    formPath: 'harsh_turning',
    apiKey: 'harsh_turning',
    label: 'scoreCards.settings.weightage.harsh.turning.title',
  },
  {
    formPath: 'harsh_braking',
    apiKey: 'harsh_braking',
    label: 'scoreCards.settings.weightage.harsh.braking.title',
  },
  {
    formPath: 'harsh_acceleration',
    apiKey: 'harsh_acceleration',
    label: 'scoreCards.settings.weightage.harsh.acceleration.title',
  },
  {
    formPath: 'harsh_rpm',
    apiKey: 'harsh_rpm',
    label: 'scoreCards.settings.weightage.harsh.rpm.title',
  },
  {
    formPath: 'idling',
    apiKey: 'driver_idling',
    label: 'scoreCards.settings.weightage.driverBehaviour.idling.title',
  },
  {
    formPath: 'aiCamera_cameraCovered',
    apiKey: 'cam_covered',
    label: 'scoreCards.settings.weightage.aiCamera.cameraCovered.title',
  },
  {
    formPath: 'aiCamera_smoking',
    apiKey: 'driver_smoking',
    label: 'scoreCards.settings.weightage.aiCamera.smoking.title',
  },
  {
    formPath: 'aiCamera_cellPhone',
    apiKey: 'driver_using_cell_phone',
    label: 'scoreCards.settings.weightage.aiCamera.cellPhone.title',
  },
  {
    formPath: 'aiCamera_distracted',
    apiKey: 'driver_distracted',
    label: 'scoreCards.settings.weightage.aiCamera.distracted.title',
  },
  {
    formPath: 'aiCamera_fatigued',
    apiKey: 'driver_fatigued',
    label: 'scoreCards.settings.weightage.aiCamera.fatigued.title',
  },
  {
    formPath: 'aiCamera_seatbelt',
    apiKey: 'driver_not_wearing_seatbelt',
    label: 'scoreCards.settings.weightage.aiCamera.seatbelt.title',
  },
  {
    formPath: 'aiCamera_forwardCollision',
    apiKey: 'forward_collision_warning_triggered',
    label: 'scoreCards.settings.weightage.aiCamera.forwardCollision.title',
  },
  {
    formPath: 'aiCamera_followingDistance',
    apiKey: 'following_distance',
    label: 'scoreCards.settings.weightage.aiCamera.followingDistance.title',
  },
  {
    formPath: 'aiCamera_collision',
    apiKey: 'collision',
    label: 'scoreCards.settings.weightage.aiCamera.collision.title',
  },
] as const satisfies Array<{
  formPath: keyof WeightageConfigType['form']
  apiKey: (typeof scorecardRawWeightageApiKeys)[number]
  label: string
}>

// Function to create schema with speeding weightage progression validation
export const createWeightageConfigSchemaWithValidation = (
  weightageLevels: Array<{
    weightageLevelId: string
    threshold: number | null
    value: string
  }>,
) =>
  baseWeightageConfigSchema
    .check((ctx) =>
      checkWeightageSpeedLevelConflict({
        ctx,
        speedingEventKeys: [
          'speeding_over',
          'speeding_10',
          'speeding_20',
          'speeding_30',
        ],
        weightageLevels,
      }),
    )
    .check((ctx) =>
      checkWeightageSpeedLevelConflict({
        ctx,
        speedingEventKeys: [
          'speeding_limit',
          'speeding_limit100',
          'speeding_limit140',
          'speeding_limit160',
        ],
        weightageLevels,
      }),
    )

// Default schema without validation (for backward compatibility)
export const weightageConfigSchema = baseWeightageConfigSchema

export type WeightageConfigType = z.infer<typeof weightageConfigSchema>

// Configuration

const durationSchema = z.object({ hour: z.number(), minute: z.number() })

export type MinimumRequirementDuration = z.infer<typeof durationSchema>

export const configurationSchema = z.object({
  form:
    // wrap the whole form within field `form` to allow to set the form with default values
    z.object({
      aiCameraEventDetection: z.object({
        on: z.boolean(),
        hasAiCamera: z.boolean(), // has vehicle with vision
      }),
      weightageCustomize: z.object({
        range: z.tuple([z.number(), z.number()]),
        on: z.boolean(),
      }),
      scorePeriod: scoreCardRefreshPeriodIdSchema,
      minimumRequirement: z.object({
        value: z.object({
          id: scoreCardScoreCountRuleIdSchema,
          distance: z.number().optional(),
          duration: durationSchema.optional(),
        }),
        on: z.boolean(),
      }),
      safetyTarget: z.object({
        value: z.number().min(1).max(MAX_SCORE),
        on: z.boolean(),
      }),
      speedingGracePeriod: z.object({
        value: z.number().min(1),
        on: z.boolean(),
      }),
    }),
})

export type ConfigurationFormType = z.infer<typeof configurationSchema>

export type ScoreItem = {
  label: string
  impact: number
}
